from typing import List, Optional, Any
from pipecat.services.openai.llm import OpenAILLMService
from models import CompanyData, AppointmentForm, PatientData, Appointment, Doctor
import utils.appointments
from constants import Intents
from .constants_specific import MOTIVE_NAMES
from .tools import CaedaTools
from nodes.base.prompts import BasePrompts


class CaedaPrompts(BasePrompts):
    def __init__(self, company_data: CompanyData, llm: OpenAILLMService):
        self.company_data = company_data
        self.llm = llm

    def _set_message(self, message: str):
        message = " ".join(
            [line.strip() for line in message.split("\n") if line.strip()]
        )
        return {"role": "system", "content": message}

    def init(self):
        message = self._set_message(
            f"""
                Dit:  'Bienvenue au {self.company_data.name}. Êtes-vous un patient ou un professionnel de santé ?'
                -- 
                Si tu as compris une des deux réponses, utilise la fonction 'handle_person_type' avec une de ces valeurs 'patient' ou 'professionnel de santé'.
                Si tu comprends pas essaye de deviner avec le mot le plus proche de sa réponse.
          '"""
        )

        tools = [CaedaTools.handle_person_type]
        return message, tools

    def init_patient(self):
        list_intents = ", ".join([intent.value for intent in Intents])

        message = self._set_message(
            f"""
                Tu dis:  Parfait, Comment puis-je vous aider ?
                -- 
                Si tu as compris une des raison de l'appel, utilise la fonction 'handle_check_intent' avec une de ces valeurs {list_intents} pour intent
                Sinon, excuse toi et redemande la raion de l'appel.
                Si l'utilisateur dit juste "rendez-vous", utilise la fonction 'handle_check_intent' avec la valeur 'nouveau'.
                Si l'utilisateur dit juste 'contacter', utilise la fonction 'handle_check_intent' avec la valeur 'question'.
                Si 'l'utilisateur veut avoir les résultats', utilise la fonction 'handle_check_intent' avec la valeur 'question'.
                Si l'utilisateur dit qu'elle a une question, utilise la fonction 'handle_check_intent' avec la valeur 'question'.
                Si l'utilisateur dit que le centre a essayé de la contacter, utilise la fonction 'forward_call'.
                Si l'utilisateur veut être transféré, utilise la fonction 'forward_call'.
                Si tu ne comprends pas la raison de l'appel, dit lui que l'utilisateur peut faire {list_intents}.
          '"""
        )

        tools = [CaedaTools.handle_check_intent, CaedaTools.forward_call]

        return message, tools

    def ask_confirm_identity(
        self, patient_data: PatientData, return_node: Optional[dict[str, Any]] = None
    ):
        return_node_name = return_node["function"]["name"] if return_node else ""

        message = self._set_message(
            f"""
                                    Dit: Est-ce que votre appel est pour {patient_data.first_name} {patient_data.last_name} ?
                                    ---
                                    Si dit oui ou non, utilise la fonction handle_confirm_identity. True si il dit oui False si il dit non.
                                    Il peut seulement dire 'oui' ou 'non'.
                                    {"Si le patient souhaite retourner à la question précédente, utilise la fonction "+return_node_name if return_node else ""}
                                    Il doit répondre à ta question.
                                    """
        )

        tools = [
            CaedaTools.handle_confirm_identity,
        ]

        tools.append(return_node) if return_node else None

        return message, tools

    async def ask_first_name(
        self,
        return_node: Optional[dict[str, Any]] = None,
        first_sentence: Optional[str] = "",
    ):
        return_node_name = return_node["function"]["name"] if return_node else ""

        message = self._set_message(
            f"""
            Dit: '{first_sentence} Quel est votre prénom ?'
            --
            une fois que tu as compris le prénom, utilise la fonction handle_first_name.
            Si le prénom te semble étrange, interprète avec ce qu'il a dit, ca doit être un prénom francais.
            {"Si le patient souhaite retourner à la question précédente, utilise la fonction "+return_node_name if return_node else ""}
            Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant son prénom.
            """
        )

        tools = [
            CaedaTools.handle_first_name,
        ]

        tools.append(return_node) if return_node else None

        return message, tools

    def ask_last_name(self):
        message = self._set_message(
            """
            Dit: 'Quel est votre nom de famille ?'.
            --
            une fois que tu as compris le nom de famille, utilise la fonction handle_last_name
            Si le nom te semble étrange, interprète avec ce qu'il a dit, ca doit être un nom francais.
            Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant son nom de famille.
            """
        )

        tools = [
            CaedaTools.handle_last_name,
        ]

        return message, tools

    def ask_birthdate_of_patient(self):
        message = self._set_message(
            """
            Dit: Quelle est la date de naissance ?
            --
            une fois que tu as compris l'âge, utilise la fonction handle_birthdate_of_patient
            Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant sa date de naissance.
            Si la date de naissance est incorrecte, demande à l'utilisateur de répéter sans donner un exemple.
            """
        )

        tools = [
            CaedaTools.handle_birthdate_of_patient,
        ]

        return message, tools

    def ask_confirm_appointment(self, appointment: AppointmentForm, intent: str):

        start_date = utils.appointments.format_datetime_with_day_in_french(
            appointment.start_date
        )
        motive = appointment.visit_motive_name

        if intent == Intents.MODIFIER.value:
            intent = "nouveau"

        message = self._set_message(
            f"""
            Dit: Confirmez-vous votre {intent} concernant votre {motive} du {start_date} avec docteur {appointment.medecin}?
            ---
            Utilise la fonction 'handle_appointment_confirmation' avec True si le client dit oui, False si le client dit non.
            Si ce n'est pas claire, répète ta question.
            Ici le patient ne peut pas te poser de question. Il peut seulement dire 'oui' ou 'non'.
            """
        )
        tools = [
            CaedaTools.handle_appointment_confirmation,
        ]

        tools = [tool for tool in tools if tool is not None]
        return message, tools

    def ask_reason_of_cancellation(self, intent: Intents = Intents.ANNULATION):
        message = self._set_message(
            f"""
            Dit: Pour quelle raison souhaitez-vous {intent.value} votre rendez-vous ?
            ---
            Utilise la fonction 'handle_reason_of_cancellation' pour comprendre la raison.
            Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant la raison de l'annulation.
            """
        )

        tools = [
            CaedaTools.handle_reason_of_cancellation,
        ]

        return message, tools

    """
    Nouveau Appointment
    """

    def ask_motive(self):
        message = self._set_message(
            f"""
            Dit: Pour quelle raison souhaitez-vous prendre rendez-vous ?
            ---
            Utilise la fonction 'handle_motive' pour comprendre la raison.
            La raison doit être [type] + [motif].
            Les types sont: {", ".join(MOTIVE_NAMES)}
            S'il dit juste "consultation" faut mettre "consultation gastro entérologue" dans le motif.
            Les motifs va se trouver dans la réponse du patient.
            Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant la raison du rendez-vous.
            """
        )

        tools = [
            CaedaTools.handle_motive,
        ]

        return message, tools

    def ask_doctor_name(
        self,
        doctors: List[Doctor],
        return_node: Optional[dict[str, Any]] = None,
    ):
        doctors_str = ", ".join([doctor.name for doctor in doctors])
        return_node_name = return_node["function"]["name"] if return_node else ""

        message = self._set_message(
            f"""
            Dit: 'Avec quel docteur souhaitez-vous prendre ce rendez-vous ?'
            ---
            Utilise la fonction 'handle_doctor_name' pour comprendre le nom du docteur.
            Le nom doit être exactement le même que dans la liste. Si le nom ne peut être aucun de la liste, demande à l'utilisateur de répéter. Il peut aussi dire 'non' si le choix n'est pas important.
            Ne dit pas mais les noms des docteurs sont: {doctors_str}
            Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant le nom du docteur.
            S'il dit 'peu importe' ou 'autre', utilise la fonction 'handle_doctor_name' avec la valeur 'autre' pour continuer.
            {"Si le patient souhaite un autre motif, utilise la fonction "+ return_node_name if return_node else ""}
            """
        )

        tools = [
            CaedaTools.handle_doctor_name,
        ]

        return message, tools

    def ask_precise_mano(self):
        message = self._set_message(
            """
            Dit: 'Souhaitez-vous une manométrie oesophagienne ou ano-rectale?"
            --
            Utilise la fonction 'handle_motive' pour comprendre la raison.
            
            """
        )
        tools = [
            CaedaTools.handle_motive,
        ]
        return message, tools

    def ask_mano_ph(self, type: str):
        other_motive = (
            "Manométrie Oesophagienne" if "ph-" in type.lower() else "pH-métrie"
        )
        message = self._set_message(
            f"""
            Dit: Est-ce que une {other_motive} est indiquée sur votre ordonnance ?
            ---
            Utilise la fonction 'handle_mano_ph' avec True si le client dit oui, False si le client dit non.
            """
        )

        tools = [
            CaedaTools.handle_mano_ph,
        ]

        return message, tools

    def ask_test_adeca_depistage(self):
        message = self._set_message(
            """
            Dit: Avez-vous été fait un test de dépistage avec un résultat positif ?
            ---
            Utilise la fonction 'handle_test_adeca_depistate' avec True si le client dit oui, False si le client dit non.
            """
        )

        tools = [
            CaedaTools.handle_test_adeca_depistate,
        ]

        return message, tools

    def ask_take_anticoagulant(self):
        message = self._set_message(
            """
            Dit: Prenez-vous un traitement anticoagulants ?
            ---
            Utilise la fonction 'handle_take_anticoagulant' avec True si le client dit oui, False si le client dit non.
            """
        )

        tools = [
            CaedaTools.handle_take_anticoagulant,
        ]

        return message, tools

    def suggest_appointment_datetime(
        self,
        closer_datetimes: List[Appointment],
        with_doctor: bool = False,
        return_node: Optional[dict[str, Any]] = None,
        first_sentence: str = "",
    ):
        closer_datetimes_str = [
            (
                utils.appointments.format_appointment_to_datetime_with_day_in_french(
                    time_slot
                )
                if with_doctor
                else utils.appointments.format_datetime_with_day_in_french(
                    time_slot.start_date
                )
            )
            for time_slot in closer_datetimes
        ]

        return_node_name = return_node["function"]["name"] if return_node else ""

        if isinstance(closer_datetimes_str, list) and len(closer_datetimes_str) > 1:
            message = self._set_message(
                f"""
                Dit: {first_sentence}. Nos deux seules disponibilités les plus proches sont : {" ou ".join(closer_datetimes_str)}. Dites moi quelle créneau vous convient.
                --
                Ici tu n'as PAS le droit de confirmer le rendez-vous ! Car il y a encore des étapes à suivre.
                Si tu n'as pas compris, dit lui de répéter 
                Si le client a choisi une des dates proposées, utilise la fonction 'handle_appointment_datetime' pour continuer.
                Si le client souhaite dit 'autre', utilise la fonction 'handle_appointment_datetime' avec la valeur 'autre' pour continuer. 
                {"Si le patient souhaite retourner à la question précédente, utilise la fonction " + return_node_name if return_node else ""}
                Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant la date du rendez-vous.
                """
            )

        else:
            message = self._set_message(
                f"""
                Dit: {first_sentence}. Notre disponibilité la plus proche est : {" ou ".join(closer_datetimes_str)}, c'est la seule disponibilité ce jour là, cela vous convient-il ?
                --
                Ici tu n'as PAS le droit de confirmer le rendez-vous ! Car il y a encore des étapes à suivre.
                Si le client a choisi la datetime proposé, utilise la fonction 'handle_appointment_datetime' avec la valeur voulu pour continuer.
                Si le client souhaite un autre jour, utilise la fonction 'handle_appointment_datetime' avec la valeur 'autre' pour continuer.
                Si le client dit 'non', utilise la fonction 'handle_appointment_datetime' avec la valeur 'autre' pour continuer.
                Si tu n'as pas compris, utilise la fonction 'handle_appointment_datetime' avec la valeur 'autre' pour continuer.
                {"Si le patient souhaite retourner à la question précédente, utilise la fonction " + return_node_name if return_node else ""}
                Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant la date du rendez-vous.
                """
            )

        tools = [
            CaedaTools.handle_appointment_datetime,
        ]

        tools.append(return_node) if return_node else None

        return message, tools

    def ask_appointment_confirmation(self, appointment: AppointmentForm):
        start_date_str = utils.appointments.format_datetime_with_day_in_french(
            appointment.start_date
        )

        message = self._set_message(
            f"""
            Dit: Confirmez-vous votre rendez-vous du {start_date_str} ?
            ---
            Utilise la fonction 'handle_appointment_confirmation' avec True si le client dit oui, False si le client dit non.
            Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant le rendez-vous.
            """
        )

        tools = [
            CaedaTools.handle_appointment_confirmation,
        ]

        return message, tools

    """
    Question
    """

    """
    Retard
    """

    def ask_how_long_late(self):
        message = self._set_message(
            """
            Dit: "De combien de temps sera votre retard ?"
            --
            Dès que tu sais le temps de retard mets le nombre dans 'minutes' et appelle la fonction 'handle_how_long_late'.
            Il doit te répondre avec un nombre.
          """
        )

        tools = [CaedaTools.handle_how_long_late]

        return message, tools
