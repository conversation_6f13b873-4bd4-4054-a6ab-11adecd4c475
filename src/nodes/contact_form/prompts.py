from abc import ABC
from typing import Optional, Any
from models import CompanyData, PatientData
from .tools import ContactFormTools


class ContactFormPrompts(ABC):
    def __init__(self, company_data: CompanyData):
        self.company_data = company_data

    def _set_message(self, message: str):
        message = " ".join(
            [line.strip() for line in message.split("\n") if line.strip()]
        )
        return {"role": "system", "content": message}

    def ask_new_or_existing_patient(
        self,
        return_node: Optional[dict[str, Any]] = None,
        first_sentence: Optional[str] = "",
    ):
        return_node_name = return_node["function"]["name"] if return_node else ""

        message = self._set_message(
            f"""
            Dit: '{first_sentence} Êtes-vous déjà venu à notre établissement ?'
            --
            une fois que tu as compris la réponse, utilise la fonction handle_new_or_existing_patient avec la valeur 'oui' ou 'non'.
            Ne répete pas la réponse une fois que tu l'as comprise.
            {"Si le patient souhaite retourner à la question précédente, utilise la fonction "+return_node_name if return_node else ""}
            Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant son statut de patient.
            """
        )

        tools = [
            ContactFormTools.handle_new_or_existing_patient,
        ]

        tools.append(return_node) if return_node else None

        return message, tools

    def ask_first_name(
        self,
        return_node: Optional[dict[str, Any]] = None,
        first_sentence: Optional[str] = "",
    ):
        return_node_name = (
            return_node["function"]["name"]
            if return_node and return_node.get("function")
            else ""
        )

        message = self._set_message(
            f"""
            Dit: '{first_sentence} Pouvez-vous dire et épeller votre prénom ?'
            --
            Le patient va le dire et l'épeler, prend en compte ce qu'il dit et épelle, pour transcrire son prénom.
            il est rare que le prénom sois en deux parties, il faut donc que tu prennes en compte ce qu'il dit et épelle.
            prend en compte les lettres épellées, pour corriger l'orthographe du prénom.
            par exemple si le patient dit téofylandre et quil eppele en t h é o p h  i l a n d r e, tu dois comprendre que c'est Théofilandre.
            une fois que tu as compris et transcrit le prénom, utilise la fonction handle_first_name.
            Ne répete pas le prénom une fois que tu l'as compris.
            Si le prénom te semble étrange, interprète avec ce qu'il a dit et épellé n'oublie pas les lettres H quipeuevnt etre silencieuses. 
            {"Si le patient souhaite retourner à la question précédente, utilise la fonction "+return_node_name if return_node else ""}
            Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant son prénom.
            """
        )

        tools = [
            ContactFormTools.handle_first_name,
        ]

        tools.append(return_node) if return_node else None

        return message, tools

    def ask_last_name(self):
        message = self._set_message(
            """
            Dit: 'Pouvez-vous dire et épeller votre nom de famille ?'.
            --
            Le patient va le dire et l'épeler, prend en compte ce qu'il dit et épelle, pour transcrire son nom de famille.
            il est rare que le nom sois en deux parties, il faut donc que tu prennes en compte ce qu'il dit et épelle.
            prend en compte les lettres épellées, pour corriger l'orthographe du nom de famille.
            par exemple si le patient dit devoknar et quil eppele en d e V a u q u e m a r e, tu dois comprendre que c'est De Vaquemare.
            une fois que tu as compris et transcris le nom de famille, utilise la fonction handle_last_name
            Si le nom te semble étrange, interprète avec ce qu'il a dit et épellé, n'oublie pas les lettres H qui peuvent etre silencieuses. 
            Ne répete pas le nom une fois que tu l'as compris.
            Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant son nom de famille.
            """
        )

        tools = [
            ContactFormTools.handle_last_name,
        ]

        return message, tools

    def ask_birthdate_contact_form(
        self,
        first_name: Optional[str] = "",
    ):

        name_str = f"de {first_name}" if first_name else "du patient"
        message = self._set_message(
            f"""
            Dit: 'Quelle est la date de naissance {name_str}, par exemple dites 29 mars, 1982' ?
            --
            une fois que tu as compris l'âge, utilise la fonction handle_birthdate_contact_form
            Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant sa date de naissance.
            """
        )

        tools = [
            ContactFormTools.handle_birthdate_contact_form,
        ]

        return message, tools

    def ask_identity_contact_form(
        self,
        patient_data: PatientData,
    ):

        message = self._set_message(
            f"""
            Dit: "Êtes-vous {patient_data.first_name} {patient_data.last_name} ?"
            --
            
            une fois que tu as compris la confirmation d'identité, utilise la fonction handle_identity_confirmation_contact_form avec la valeur 'oui' ou 'non'.
            Ne répete pas la confirmation d'identité une fois que tu l'as comprise.
            Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant son identité.
            """
        )
        tools = [
            ContactFormTools.handle_identity_confirmation_contact_form,
        ]

        return message, tools
