import asyncio
import os
import requests
from datetime import datetime, timedelta

from pipecat.services.openai.llm import OpenAILLMContextFrame

from pipecat.processors.frame_processor import FrameDirection

from loguru import logger
from dotenv import load_dotenv


from utils.legacy import convert_utc_to_french_text
from utils.legacy import timing_config

from nodes.base.legacy_intake_processor import BaseIntakeProcessor


# Load environment variables
load_dotenv(override=True)

from audios import load_sounds

from supabase import create_client

# Load sound files
sounds = load_sounds()

# Initialize Supabase client
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_KEY")

try:
    supabase = create_client(supabase_url, supabase_key)
except Exception as e:
    logger.error(f"Failed to initialize Supabase client: {str(e)}")
    supabase = None

from models import CompanyData


class IntakeProcessor(BaseIntakeProcessor):
    def __init__(
        self, llm, context, company_data: CompanyData, phone_caller, patient_data
    ):
        self.configContent = company_data.model_dump()
        super().__init__(llm, context, company_data, phone_caller, patient_data)
        self._appointment_confirmed = False
        self._before_instructions_given = False
        self._api_call_in_progress = False
        self._ask_date_called = False

    def _initialize_context(self):
        self._context.add_message(
            {
                "role": "system",
                "content": f"Dis quelque chose comme : 'Bienvenue au centre {self.configContent['name']}. Comment puis-je vous aider ?' Une fois que tu arrives à estimer l'intention de l'utilisateur utilise la fonction check_intent. Si le patient répond juste oui, redemande au patient pourquoi il a appelé.",
            }
        )
        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "check_intent",
                        "description": "utilise cette fonction lorsque tu arrives à estimer l'intention de l'utilisateur. Si l'utilisateur souhaite parler à un médecin son intention est question.",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "intent": {
                                    "type": "string",
                                    "description": "intention de l'utilisateur",
                                    "enum": [
                                        "rendez-vous",
                                        "question",
                                        "urgence",
                                        "retard",
                                        "annulation",
                                    ],
                                }
                            },
                            "required": ["intent"],
                        },
                    },
                }
            ]
        )

    async def check_intent(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        # intent = args["intent"]

        await self.search_patient(
            function_name, tool_call_id, args, llm, context, result_callback
        )

    async def forward_call(
        self,
        function_name,
        tool_call_id,
        args,
        llm,
        context,
        result_callback,
        forward_number=None,
    ):
        # Extract forward_number from args if it exists, otherwise use the provided forward_number
        forward_number = (
            args.get("forward_number")
            or forward_number
            or self.configContent["forward_number"]
        )
        await super().forward_call(
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback,
            forward_number=forward_number,
        )

    async def search_patient(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        if self._patient_data.get("first_name") is not None:
            if args["intent"] == "rendez-vous":
                self._context.set_tools(
                    [
                        {
                            "type": "function",
                            "function": {
                                "name": "set_appt",
                                "description": "utilise cette fonction si l'identité est correcte",
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "appt_type",
                                "description": "utilise cette fonction si l'utilisateur dit non",
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "new_patient": {
                                            "type": "boolean",
                                            "description": "True si lutilisateur a dit non, ce n'est pas le patient reconnu",
                                        }
                                    },
                                    "required": ["new_patient"],
                                },
                            },
                        },
                    ]
                )
                self._context.add_message(
                    {
                        "role": "system",
                        "content": f"Dis : Appelez-vous pour {self._patient_data['first_name']} {self._patient_data['last_name']} ? Si oui utilise la fonction set_appt, si non utilise la fonction set_question.",
                    }
                )
                await llm.process_frame(
                    OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
                )

            elif args["intent"] == "question":
                self._context.set_tools(
                    [
                        {
                            "type": "function",
                            "function": {
                                "name": "set_question",
                                "description": "utilise cette fonction si l'identité est correcte",
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "set_question",
                                "description": "utilise cette fonction si l'utilisateur dit non",
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "new_patient": {
                                            "type": "boolean",
                                            "description": "True si lutilisateur a dit non",
                                        }
                                    },
                                    "required": ["new_patient"],
                                },
                            },
                        },
                    ]
                )
                self._context.add_message(
                    {
                        "role": "system",
                        "content": f"Dis : Appelez-vous pour {self._patient_data['first_name']} {self._patient_data['last_name']} ? Une fois que l'utilisateur a répondu utilise la fonction set_question.",
                    }
                )
                await llm.process_frame(
                    OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
                )

            elif args["intent"] == "urgence":
                self._context.set_tools(
                    [
                        {
                            "type": "function",
                            "function": {
                                "name": "urgent",
                                "description": "utilise cette fonction si l'identité est correcte",
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "urgent",
                                "description": "utilise cette fonction si l'utilisateur dit non",
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "new_patient": {
                                            "type": "boolean",
                                            "description": "True si lutilisateur a dit non",
                                        }
                                    },
                                    "required": ["new_patient"],
                                },
                            },
                        },
                    ]
                )
                self._context.add_message(
                    {
                        "role": "system",
                        "content": f"Dis : Appelez-vous pour {self._patient_data['first_name']} {self._patient_data['last_name']} ? Une fois que l'utilisateur a répondu utilise la fonction urgent.",
                    }
                )
                await llm.process_frame(
                    OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
                )

            elif args["intent"] == "retard":
                self._context.set_tools(
                    [
                        {
                            "type": "function",
                            "function": {
                                "name": "retard",
                                "description": "utilise cette fonction si l'identité est correcte",
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "set_question",
                                "description": "utilise cette fonction si l'utilisateur dit non",
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "new_patient": {
                                            "type": "boolean",
                                            "description": "True si lutilisateur a dit non",
                                        }
                                    },
                                    "required": ["new_patient"],
                                },
                            },
                        },
                    ]
                )
                self._context.add_message(
                    {
                        "role": "system",
                        "content": f"Dis : Appelez-vous pour {self._patient_data['first_name']} {self._patient_data['last_name']} ? Une fois que l'utilisateur a répondu utilise la fonction retard. Si le patient dit non ou nom ou plus tard, ou souhaite plutôt l'après-midi ou le matin, ou souhaite plutôt l'après-midi ou le matin, ou souhaite plutôt l'après-midi ou le matin, utilisez la fonction set_question.",
                    }
                )
                await llm.process_frame(
                    OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
                )

            elif args["intent"] == "annulation":
                self._context.set_tools(
                    [
                        {
                            "type": "function",
                            "function": {
                                "name": "appt_intent",
                                "description": "utilise cette fonction si l'identité est correcte",
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "intent": {
                                            "type": "string",
                                            "description": "annuler si l`identité est correcte ou l`utilisateur dit oui",
                                        }
                                    },
                                    "required": ["intent"],
                                },
                            },
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "set_question",
                                "description": "utilise cette fonction si l'utilisateur dit non",
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "new_patient": {
                                            "type": "boolean",
                                            "description": "True si lutilisateur a dit non",
                                        }
                                    },
                                    "required": ["new_patient"],
                                },
                            },
                        },
                    ]
                )
                self._context.add_message(
                    {
                        "role": "system",
                        "content": f"Dis : tes-vous bien {self._patient_data['first_name']} {self._patient_data['last_name']} ? Une fois que l'utilisateur a répondu utilise la fonction appt_intent. Si le patient dit non ou nom ou plus tard, ou souhaite plutôt l'après-midi ou le matin, ou souhaite plutôt l'après-midi ou le matin, ou souhaite plutôt l'après-midi ou le matin, utilisez la fonction set_question.",
                    }
                )
                await llm.process_frame(
                    OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
                )

        else:
            if args["intent"] == "rendez-vous":
                await self.appt_type(
                    function_name,
                    tool_call_id,
                    {"new_patient": True},
                    llm,
                    context,
                    result_callback,
                )
            elif args["intent"] == "urgence":
                await self.urgent(
                    function_name,
                    tool_call_id,
                    {"new_patient": True},
                    llm,
                    context,
                    result_callback,
                )
            elif (
                args["intent"] == "question"
                or args["intent"] == "retard"
                or args["intent"] == "annulation"
            ):
                await self.set_question(
                    function_name,
                    tool_call_id,
                    {"new_patient": True},
                    llm,
                    context,
                    result_callback,
                )

    async def retard(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        api_url = (
            "https://doctolib-secret-scrape-global.onrender.com/api/v1/modify-appt"
        )
        request_body = {
            "config": self._config,
            "id": self._patient_data.get("appt_id"),
            "note": "Le patient a signalé un retard au téléphone par Vocca",
        }
        headers = {
            "Authorization": f"Bearer {self._api_token}",
            "Content-Type": "application/json",
        }

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "set_question",
                        "description": "Utilise cette fonction si le patient a d'autres questions",
                    },
                }
            ]
        )

        asyncio.create_task(
            asyncio.to_thread(requests.put, api_url, json=request_body, headers=headers)
        )

        self._context.add_message(
            {
                "role": "system",
                "content": "J'ai bien signalé votre retard au médecin, si le retard est trop important, il est possible que le rendez-vous soit annulé.en signalé votre retard au médecin.",
            }
        )

        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def urgent(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        self._new_patient = args.get("new_patient", False)

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "appt_type",
                        "description": "utilise cette fonction si l'utilisateur souhaite un rendez-vous d'urgence",
                    },
                },
                {
                    "type": "function",
                    "function": {
                        "name": "set_question",
                        "description": "utilise cette fonction si l'utilisateur souhaite laisser une note d'urgence au centre",
                    },
                },
            ]
        )

        self._context.add_message(
            {
                "role": "system",
                "content": "Est-ce pour un rendez-vous d'urgence ou souhaitez vous laisser une note d'urgence au centre ? Pour une urgence médicale suggère au patient de contacter l'hôpital le plus proche. Si l'utilisateur souhaite laisser une note d'urgence utilise la fonction set_question. Si l'utilisateur souhaite un rendez-vous d'urgence utilisa la fonction appt_type.",
            }
        )
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def set_appt(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        if self._new_patient:
            await self.appt_type(
                function_name,
                tool_call_id,
                {"new_patient": True},
                llm,
                context,
                result_callback,
            )
            return

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "appt_intent",
                        "description": "utilise cette fonction lorsque tu as compris l'intention de l'utilisateur",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "intent": {
                                    "type": "string",
                                    "description": "intention de l'utilisateur",
                                    "enum": ["nouveau", "modifier", "annuler"],
                                }
                            },
                            "required": ["intent"],
                        },
                    },
                }
            ]
        )
        self._context.add_message(
            {
                "role": "system",
                "content": "Parfait. Souhaitez-vous un nouveau rendez-vous annuler ou modifier un rendez-vous ? Une fois que l'utilisateur a répondu utilise la fonction appt_intent.",
            }
        )
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def appt_type(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        self._starting_double_appointment = False

        self._new_patient = args.get("new_patient", False)

        content = (
            "Merci d'avoir pris contact avec Union Imagerie, vous pouvez prendre rendez-vous sur internet avec le lien suivant : "
            + self.configContent["booking_url"]
        )

        webhook_url = "https://n8n-self-hosted-vocca.onrender.com/webhook/46ace88a-b96e-4900-8dc3-4e5210f69d53"
        payload = {
            "to": "" + self._caller_phone_number,
            "content": content,
            "from": "Union",
        }

        asyncio.create_task(asyncio.to_thread(requests.post, webhook_url, json=payload))

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "appt_unique_type",
                        "description": "Utilise cette fonction si le patient souhaite un seul type d'examen",
                    },
                },
                {
                    "type": "function",
                    "function": {
                        "name": "appt_double_type",
                        "description": "Utilise cette fonction si le patient souhaite deux examens",
                    },
                },
            ]
        )

        self._context.add_message(
            {
                "role": "system",
                "content": "dis : 'Je viens de vous envoyer le lien de réservation par SMS. Sinon vous pouvez prendre rendez-vous avec moi.' ensuite Si le patient souhaite prendre rendez-vous demande : Souhaitez-vous un seul type d'examen ou deux examens ? Si le patient souhaite un double rendez-vous utilise la fonction appt_double_type. Si le patient souhaite un seul rendez-vous utilise la fonction appt_unique_type.",
            }
        )
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def appt_double_type(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        if self._starting_double_appointment:
            return

        self._starting_double_appointment = True

        additional_info = args.get("additional_info", None)

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "appt_double_type_1",
                        "description": "Utilise cette fonction lorsque tu as compris le premier type de rendez-vous précis souhaité",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "visit_motive_1": {
                                    "type": "string",
                                    "description": "premier type de rendez-vous précis souhaité",
                                }
                            },
                            "required": ["visit_motive_1"],
                        },
                    },
                }
            ]
        )

        self._context.add_message(
            {
                "role": "system",
                "content": f"Dis : '{additional_info} Quel est le premier type d'examen souhaité ?' Si le patient réponds uniquement Échographie, Radiographie, Écho-Doppler, Scanner, IRM, Sclérose, demande pour quelle partie du corps le patient souhaite l'examen. Les examens peuvent aussi être des ostéodensitométries, des mammographies, des consultations Angiologie et des scléroses pas besoin de demander la partie du corps pour ceux-ci. Ne demande pas le deuxième type d'examen ici. Une fois que le patient a répondu, utilise la fonction appt_double_type_1 une seule fois.",
            }
        )
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def appt_double_type_1(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        self._visit_motive_1 = args["visit_motive_1"]

        self._checking_double_appointment = False

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "appt_check_double_type",
                        "description": "Utilise cette fonction lorsque tu as compris le second type de rendez-vous précis souhaité par le patient",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "visit_motive_2": {
                                    "type": "string",
                                    "description": "second type de rendez-vous précis souhaité",
                                }
                            },
                            "required": ["visit_motive_2"],
                        },
                    },
                }
            ]
        )

        self._context.add_message(
            {
                "role": "system",
                "content": "Demande : 'Quel est le second type d'examen souhaité ?' Si le patient réponds uniquement Échographie, Radiographie, Écho-Doppler, Scanner, IRM, Sclérose, demande pour quelle partie du corps le patient souhaite l'examen. Les examens peuvent aussi être des ostéodensitométries, des mammographies, des consultations Angiologie et des scléroses. Une fois que le patient a répondu, utilise la fonction appt_check_double_type.",
            }
        )
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def appt_unique_type(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        additional_info = args.get("additional_info", None)

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "appt_check_type",
                        "description": "Utilise cette fonction lorsque tu as compris le type de rendez-vous précis souhaité par le patient",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "visit_motive": {
                                    "type": "string",
                                    "description": "type de rendez-vous précis souhaité",
                                }
                            },
                            "required": ["visit_motive"],
                        },
                    },
                }
            ]
        )

        self._context.add_message(
            {
                "role": "system",
                "content": f"Dis : '{additional_info} Quel type d'examen souhaitez-vous ?' Si le patient réponds uniquement Échographie, Radiographie, Écho-Doppler, Scanner, IRM, Sclérose, demande pour quelle partie du corps le patient souhaite l'examen. Les examens peuvent aussi être des ostéodensitométries, des mammographies, des consultations Angiologie et des scléroses. Une fois que le patient a répondu, utilise la fonction appt_check_type.",
            }
        )
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def appt_check_double_type(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        if self._checking_double_appointment:
            return

        self._checking_double_appointment = True

        # Vérifier si IRM ou scanner sont dans l'un des deux motifs de visite, uniquement pour la config48
        if self._config == "config48" and (
            "IRM" in self._visit_motive_1.upper()
            or "SCANNER" in self._visit_motive_1.upper()
            or "IRM" in args["visit_motive_2"].upper()
            or "SCANNER" in args["visit_motive_2"].upper()
        ):
            await self.scanner(
                function_name, tool_call_id, args, llm, context, result_callback
            )
            return

        self._api_call_in_progress = True

        self._visit_motive_2 = args["visit_motive_2"]

        async def make_api_call(visit_motive):
            api_url = "https://doctolib-secret-scrape-global.onrender.com/api/v1/get-visit-motive-id"
            request_body = {"config": self._config, "practice": visit_motive}
            headers = {
                "Authorization": f"Bearer {self._api_token}",
                "Content-Type": "application/json",
            }
            response = await asyncio.to_thread(
                requests.post, api_url, json=request_body, headers=headers
            )
            return response.json()

        # Run both API calls and TTS concurrently
        api_result_1, api_result_2, _ = await asyncio.gather(
            make_api_call(self._visit_motive_1),
            make_api_call(self._visit_motive_2),
            self._tts.say("Je consulte nos motifs de rendez-vous."),
        )

        self._api_call_in_progress = False

        # Process the results of both API calls
        data_1 = api_result_1
        visit_motive_id_1 = data_1.get("visit_motive_id")
        data_2 = api_result_2
        visit_motive_id_2 = data_2.get("visit_motive_id")

        # Check if visit motive IDs are the same
        if visit_motive_id_1 == visit_motive_id_2:
            self._starting_double_appointment = False
            self._checking_double_appointment = False
            await self.appt_double_type(
                function_name,
                tool_call_id,
                {
                    "additional_info": "Désolé mais je n'ai pas trouvé vos deux motifs d'examen. Reprenons depuis le début."
                },
                llm,
                context,
                result_callback,
            )
            return

        # Check if both visit motives contain "IRM"
        if (
            "IRM" in self._visit_motive_1.upper()
            and "IRM" in args["visit_motive_2"].upper()
        ):
            self._context.add_message(
                {
                    "role": "system",
                    "content": "Je suis désolé, il n'est pas possible de prendre deux rendez-vous d'IRM dans la même journée. Je vais laisser une note au secrétariat pour qu'ils puissent puisse vous recontacter et organiser vos rendez-vous sur des jours différents. Tu ne peux pas vérifier les disponibilités pour les deux rendez-vous ni prendre de rendez-vous.",
                }
            )
            await llm.process_frame(
                OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
            )
            return

        price_1 = data_1.get("price", "")
        price_2 = data_2.get("price", "")
        instructions_1 = data_1.get("instructions", "")
        instructions_2 = data_2.get("instructions", "")

        open_1 = data_1.get("open", "")
        open_2 = data_2.get("open", "")

        if open_1 == False or open_2 == False:
            await self.set_question(
                function_name,
                tool_call_id,
                {"new_patient": self._new_patient},
                llm,
                context,
                result_callback,
            )
            return

        price_message_1 = (
            f" Pour cet examen, le dépassement d'honoraires est de {price_1} euros, le centre n'avance pas les frais de mutuelle."
            if price_1
            else ""
        )
        price_message_2 = (
            f" Pour cet examen, le dépassement d'honoraires est de {price_2} euros, le centre n'avance pas les frais de mutuelle."
            if price_2
            else ""
        )

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "ask_date",
                        "description": "Utilise cette fonction si les types de rendez-vous sont corrects",
                    },
                },
                {
                    "type": "function",
                    "function": {
                        "name": "appt_double_type",
                        "description": "Utilise cette fonction si les types de rendez-vous sont incorrects",
                    },
                },
            ]
        )

        if (
            visit_motive_id_1 != "error"
            and visit_motive_id_1 is not None
            and visit_motive_id_2 != "error"
            and visit_motive_id_2 is not None
        ):
            self._appt_data["visit_motive_name_1"] = data_1.get("visit_motive_name")
            self._appt_data["visit_motive_name_2"] = data_2.get("visit_motive_name")
            self._appt_data["visit_motive_id_1"] = visit_motive_id_1
            self._appt_data["visit_motive_id_2"] = visit_motive_id_2
            self._appt_data["visit_motive_name"] = (
                f"{data_1['visit_motive_name']} + {data_2['visit_motive_name']}"
            )
            self._appt_data["after"] = (
                "Pour le premier rendez-vous "
                + instructions_1
                + " et pour le second rendez-vous "
                + instructions_2
            )

            if price_message_1 or price_message_2:
                self._appt_data["before"] = (
                    "Pour le premier rendez-vous "
                    + price_message_1
                    + " et pour le second rendez-vous "
                    + price_message_2
                )
            else:
                self._appt_data["before"] = ""

            self._context.add_message(
                {
                    "role": "system",
                    "content": f"Demande au patient s'il souhaite bien prendre rendez-vous pour {data_1['visit_motive_name'].lower()} et {data_2['visit_motive_name'].lower()}. Demande bien pour ce motif : {data_1['visit_motive_name'].lower()} et {data_2['visit_motive_name'].lower()} pas un autre. Si c'est correct, utilisez la fonction ask_date. Sinon, utilisez la fonction appt_double_type.",
                }
            )
            await llm.process_frame(
                OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
            )
        else:
            self._starting_double_appointment = False
            await self.appt_double_type(
                function_name,
                tool_call_id,
                {"additional_info": "Je n'ai pas trouvé vos motifs d'examens."},
                llm,
                context,
                result_callback,
            )

    def __init_nodes__(self):
        function_calls = []
        for func_name in [
            "forward_call",
            "check_intent",
            "set_appt",
            "appt_intent",
            "appt_confirm",
            "set_question",
            "recap_question",
            "appt_check_type",
            "ask_date",
            "check_availability",
            "confirm_appt",
            "cancel_appt",
            "save_new_patient_info",
            "confirm_new_patient_appt",
            "appt_type",
            "child_check",
            "urgent",
            "route_center",
            "retard",
            "no_appt",
            "appt_double_type",
            "appt_double_type_1",
            "appt_unique_type",
            "appt_check_double_type",
            "get_last_name",
            "get_birth_date",
            "general_type",
            "end_call",
            # "unrecognized_retard",
            "scanner",
            # "irm",
        ]:
            function_calls.append((func_name, self))

        return function_calls

    async def scanner(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "forward_call",
                        "description": "Utilise cette fonction pour transférer l'appel vers l'autre centre",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "forward_number": {
                                    "type": "string",
                                    "description": "Le numéro de téléphone vers lequel transférer l'appel",
                                }
                            },
                            "required": ["forward_number"],
                        },
                    },
                }
            ]
        )

        self._context.add_message(
            {
                "role": "system",
                "content": "Je suis désolé mais notre centre n'effectue pas d'IRM ni de scanner. Je peux vous orienter vers le centre d'imagerie Oudinot qui effectue ces examens. Souhaitez-vous que je leur transfère votre appel ? Si oui utilise la fonction forward_call avec le numéro +33140590808",
            }
        )
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def appt_check_type(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        # Vérifier si IRM ou scanner sont dans le motif de visite, uniquement pour la config48
        if self._config == "config48" and (
            "IRM" in args["visit_motive"].upper()
            or "SCANNER" in args["visit_motive"].upper()
        ):
            await self.scanner(
                function_name, tool_call_id, args, llm, context, result_callback
            )
            return

        self._api_call_in_progress = True

        async def make_api_call():
            api_url = "https://doctolib-secret-scrape-global.onrender.com/api/v1/get-visit-motive-id"
            request_body = {"config": self._config, "practice": args["visit_motive"]}
            headers = {
                "Authorization": f"Bearer {self._api_token}",
                "Content-Type": "application/json",
            }
            response = await asyncio.to_thread(
                requests.post, api_url, json=request_body, headers=headers
            )
            return response.json()

        # Run API call and TTS concurrently
        api_result, _ = await asyncio.gather(
            make_api_call(), self._tts.say("Je consulte nos motifs de rendez-vous.")
        )

        self._api_call_in_progress = False

        data = api_result
        visit_motive_id = data.get("visit_motive_id")
        instructions = data.get("instructions", "")
        price = data.get("price", "")
        open = data.get("open", "")

        if price != "":
            price = (
                " Pour cet examen, le dépassement d'honoraires est de "
                + price
                + " euros, le centre n'avance pas les frais de mutuelle."
            )

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "ask_date",
                        "description": "Utilise cette fonction si le type de rendez-vous est correct",
                    },
                },
                {
                    "type": "function",
                    "function": {
                        "name": "appt_unique_type",
                        "description": "Utilise cette fonction si le type de rendez-vous est incorrect",
                    },
                },
            ]
        )

        if open == False:
            await self.set_question(
                function_name,
                tool_call_id,
                {"new_patient": self._new_patient},
                llm,
                context,
                result_callback,
            )
            return

        if visit_motive_id != "error" and visit_motive_id is not None:
            self._appt_data["visit_motive_id"] = visit_motive_id
            self._appt_data["visit_motive_name"] = data.get("visit_motive_name")
            self._appt_data["before"] = price
            self._appt_data["after"] = instructions
            self._context.add_message(
                {
                    "role": "system",
                    "content": f"Demande au patient s'il souhaite bien prendre rendez-vous pour {data['visit_motive_name'].lower()}. Demande bien pour ce motif : {data['visit_motive_name'].lower()} pas un autre. Si c'est le bon type de rendez-vous utilise la fonction ask_date. Sinon utilise la fonction appt_unique_type.",
                }
            )
            await llm.process_frame(
                OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
            )
        else:
            await self.appt_unique_type(
                function_name,
                tool_call_id,
                {"additional_info": "Je n'ai pas trouvé votre type d'examen."},
                llm,
                context,
                result_callback,
            )

    async def ask_date(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        self._availability_checked = False

        self._appt_data["agenda_id"] = self.configContent["agenda_id"]

        # Only include before instructions if they haven't been given
        before_message = (
            f"{self._appt_data['before']} "
            if not self._before_instructions_given
            else ""
        )
        self._before_instructions_given = True  # Set flag after first use

        if self._ask_date_called:
            message = (
                "D'accord, à quelle date et heure préférez-vous votre rendez-vous ?"
            )
            self._context.set_tools(
                [
                    {
                        "type": "function",
                        "function": {
                            "name": "check_availability",
                            "description": "Utilise cette fonction pour vérifier les disponibilités à la date et l'heure spécifiées. Si le patient souhaite le plus tôt possible, inscris la date du lendemain.",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "time": {
                                        "type": "string",
                                        "description": "La date et l'heure souhaités pour le ou les rendez-vous",
                                        "format": "date-time",
                                        "example": "2024-08-31T14:55:01.123456+00:00",
                                    },
                                },
                                "required": ["time"],
                            },
                        },
                    }
                ]
            )

            self._context.add_message(
                {
                    "role": "system",
                    "content": f"{before_message}{message} Le patient peut souhaiter le plus tôt possible. Une fois que le patient a donné une date et une heure, utilisez la fonction check_availability pour vérifier les disponibilités.",
                }
            )
            await llm.process_frame(
                OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
            )
        else:
            self._ask_date_called = True

            tomorrow = datetime.now().date() + timedelta(days=1)
            # Convert date object to ISO format string with time
            tomorrow_str = tomorrow.strftime("%Y-%m-%d") + "T08:00:00+00:00"
            await self.check_availability(
                function_name,
                tool_call_id,
                {"time": tomorrow_str},
                llm,
                context,
                result_callback,
            )

    async def check_availability(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        if self._availability_checked == True:
            return

        self._availability_checked = True

        self._api_call_in_progress = True

        current_date = datetime.now().strftime("%d %B %Y")

        # Check if this is a double appointment by checking if visit_motive_id_1 exists in appt_data
        is_double_appointment = "visit_motive_id_1" in self._appt_data

        if is_double_appointment:
            api_url = "https://hook.eu1.make.com/q8b6iygl0d0v8jsosj2ylng7vi3nsnuq"
            request_body = {
                "config": self._config,
                "visit_motive_id_1": self._appt_data["visit_motive_id_1"],
                "visit_motive_id_2": self._appt_data["visit_motive_id_2"],
                "agenda_id": str(self._appt_data["agenda_id"]),
                "date": args["time"],
                "when": current_date,
            }
        else:
            api_url = "https://hook.eu1.make.com/eyglocxmnyqa9f5eyr4t4vah9hx6h92g"
            request_body = {
                "config": self._config,
                "visit_motive_id": self._appt_data["visit_motive_id"],
                "agenda_id": str(self._appt_data["agenda_id"]),
                "date": args["time"],
                "when": current_date,
            }

        async def make_api_call():

            headers = {
                "Authorization": f"Bearer {self._api_token}",
                "Content-Type": "application/json",
            }
            response = await asyncio.to_thread(
                requests.post, api_url, json=request_body, headers=headers
            )
            return response.json()

        # Run API call and TTS concurrently
        api_result, _ = await asyncio.gather(
            make_api_call(),
            self._tts.say(
                "Veuillez patienter s'il-vous-plaît, je vérifie nos disponibilités."
            ),
        )

        self._api_call_in_progress = False

        data = api_result

        start_date = data.get("start_date")
        end_date = data.get("end_date")
        equipment_agenda_id = data.get("equipment_agenda_id")
        agenda_id = data.get("agenda_id")
        practitioner_agenda_id = data.get("practitioner_agenda_id")
        steps = data.get("steps")

        if self._new_patient:
            self._context.set_tools(
                [
                    {
                        "type": "function",
                        "function": {
                            "name": "save_new_patient_info",
                            "description": "Utilise cette fonction pour obtenir l'identité du patient",
                        },
                    },
                    {
                        "type": "function",
                        "function": {
                            "name": "ask_date",
                            "description": "Utilise cette fonction si le patiente ne souhaite pas le rendez-vous",
                        },
                    },
                ]
            )
        else:
            self._context.set_tools(
                [
                    {
                        "type": "function",
                        "function": {
                            "name": "confirm_appt",
                            "description": "Utilise cette fonction pour confirmer le rendez-vous si le patient a répondu",
                        },
                    },
                    {
                        "type": "function",
                        "function": {
                            "name": "ask_date",
                            "description": "Utilise cette fonction si le patient ne souhaite pas le rendez-vous",
                        },
                    },
                ]
            )

        if start_date != "error" and start_date is not None:
            self._appt_data["start_date"] = start_date
            self._appt_data["end_date"] = end_date
            self._appt_data["equipment_agenda_id"] = equipment_agenda_id
            self._appt_data["practitioner_agenda_id"] = practitioner_agenda_id
            self._appt_data["agenda_id"] = agenda_id
            self._appt_data["steps"] = steps
            lisible_date = convert_utc_to_french_text(start_date)

            # Compare requested date with available date
            requested_date = datetime.fromisoformat(args["time"].replace("Z", "+00:00"))
            available_date = datetime.fromisoformat(start_date.replace("Z", "+00:00"))

            date_message = f"Le rendez-vous le plus proche est le {lisible_date}."

            # Get tomorrow's date
            tomorrow = datetime.now().date() + timedelta(days=1)

            # Only show the sorry message if the requested date is not tomorrow
            if (
                requested_date.date() != available_date.date()
                and requested_date.date() != tomorrow
            ):
                date_message = f"Je suis désolé, il n'y a pas de disponibilité à la date que vous m'avez demandée. {date_message}"

            if self._new_patient:
                self._context.add_message(
                    {
                        "role": "system",
                        "content": f"{date_message} Cela vous convient-il ? Tu ne peux pas proposer d'autre rendez-vous. Il n'y a pas de rendez-vous disponible plus tôt. Le rendez-vous n'est pas encore confirmé à cette étape. Si le patient dit oui, utilisez la fonction save_new_patient_info. Si le patient dit non ou nom ou plus tard, ou souhaite plutôt l'après-midi ou le matin, utilisez la fonction ask_date. Garde le format de date en toute lettre.",
                    }
                )
            else:
                self._context.add_message(
                    {
                        "role": "system",
                        "content": f"{date_message} Cela vous convient-il ? Tu ne peux pas proposer d'autre rendez-vous. Il n'y a pas de rendez-vous disponible plus tôt. Le rendez-vous n'est pas encore confirmé à cette étape. Si le patient dit oui, utilisez la fonction confirm_appt. Si le patient dit non ou nom ou plus tard, ou souhaite plutôt l'après-midi ou le matin, utilisez la fonction ask_date. Garde le format de date en toute lettre.",
                    }
                )
        else:
            self._context.set_tools(
                [
                    {
                        "type": "function",
                        "function": {
                            "name": "set_question",
                            "description": "Utilise cette fonction si le patient a d'autres questions",
                        },
                    }
                ]
            )

            self._context.add_message(
                {
                    "role": "system",
                    "content": "Je suis désolé mais il n'y a pas de rendez-vous disponible actuellement. Je vous invite à réessayer plus tard, de nouveaux créneaux sont ouverts chaque jour.",
                }
            )

        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def set_question(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        timing_config.TRANSCRIPTION_BUFFER_DELAY = 1.5

        add_string = ""

        self._new_patient = args.get("new_patient", False)

        if self._new_patient:
            self._patient_data = {}

            add_string = "Demande tout d'abord de dire et épeler le prénom, puis de dire et épeler le nom de famille, puis enfin la date de naissance du patient. En trois étapes."

            self._context.set_tools(
                [
                    {
                        "type": "function",
                        "function": {
                            "name": "recap_question",
                            "description": "utilise cette fonction si tu n'as pas résolu le problème du patient",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "problem": {
                                        "type": "string",
                                        "description": "Le problème détaillé du patient",
                                    },
                                    "fullname": {
                                        "type": "string",
                                        "description": "Le nom et le prénom du patient, doit correspondre à un prénom et un nom de famille crédibles français",
                                    },
                                    "birthdate": {
                                        "type": "string",
                                        "description": "La date de naissance du patient au format DD/MM/YYYY",
                                    },
                                },
                                "required": ["problem"],
                            },
                        },
                    },
                    {
                        "type": "function",
                        "function": {
                            "name": "set_appt",
                            "description": "utilise cette fonction lorsque le patient souhaite prendre un rendez-vous, annuler ou modifier un rendez-vous",
                        },
                    },
                ]
            )

        else:
            self._context.set_tools(
                [
                    {
                        "type": "function",
                        "function": {
                            "name": "recap_question",
                            "description": "utilise cette fonction si tu n'as pas résolu le problème du patient",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "problem": {
                                        "type": "string",
                                        "description": "Le problème détaillé du patient",
                                    }
                                },
                                "required": ["problem"],
                            },
                        },
                    },
                    {
                        "type": "function",
                        "function": {
                            "name": "set_appt",
                            "description": "utilise cette fonction lorsque le patient souhaite prendre un rendez-vous, annuler ou modifier un rendez-vous",
                        },
                    },
                ]
            )

        self._context.add_message(
            {
                "role": "system",
                "content": add_string
                + """
            
            Si tu peux répondre à la question, essaie d'aider le patient. Si tu n'as pas les informations nécessaires, propose de laisser une note au secrétariat. Pose des questions pour permettre au patient d'expliquer son problème.

            Uniquement si le patient parle de résultats ou de compte-rendus tu peux expliquer :
            Les résultats d'examens sont disponibles sur le portail de connexion. Les identifiants de connexion sont inscrit sur l'étiquette présente sur vos images et votre dossier donné au centre d'examen. Le mot de passe correspond à votre date de naissance. Si vous avez perdus ces identifiants il faut repasser au centre.
            Si le portail d'examen ne fonctionne pas, vous pouvez venir les chercher sur place ou je peux laisser un message pour le personnel médical.
            
            Si le problème semble urgent, demande au patient de contacter l'hôpital le plus proche'. 

            Si tu n'as pas résolu le problème puis utiliser le fonction recap_question. 

            Tu ne peux pas consulter les disponibilités ni prendre un rendez-vous à cette étape. 
            
            Si le patient souhaite finalement prendre, modifer, annuler un rendez-vous, utilise la fonction set_appt.""",
            }
        )
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def recap_question(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "set_question",
                        "description": "Utilise cette fonction si le patient a d'autres questions",
                    },
                }
            ]
        )

        if "fullname" in args:
            self._patient_data["fullname"] = args["fullname"]

        if "birthdate" in args:
            self._patient_data["birthdate"] = args["birthdate"]

        self._context.add_message(
            {
                "role": "system",
                "content": "Explique que tu laisses une note au secrétariat. Demande si le patient a d'autres précisions.",
            }
        )
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def save_new_patient_info(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        timing_config.TRANSCRIPTION_BUFFER_DELAY = 3
        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "get_last_name",
                        "description": "Utilise cette fonction une fois que tu as le prénom du patient",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "first_name": {
                                    "type": "string",
                                    "description": "Le prénom du patient",
                                }
                            },
                            "required": ["first_name"],
                        },
                    },
                }
            ]
        )
        self._context.add_message(
            {
                "role": "system",
                "content": "Demandez au patient de dire et épeler son prénom. Une fois que vous avez le prénom, utilisez la fonction get_last_name.",
            }
        )
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def get_last_name(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        timing_config.TRANSCRIPTION_BUFFER_DELAY = 3
        self._temp_patient_data = {"first_name": args["first_name"]}

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "get_birth_date",
                        "description": "Utilise cette fonction une fois que tu as le nom de famille du patient",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "last_name": {
                                    "type": "string",
                                    "description": "Le nom de famille du patient",
                                }
                            },
                            "required": ["last_name"],
                        },
                    },
                }
            ]
        )
        self._context.add_message(
            {
                "role": "system",
                "content": "Maintenant, demandez au patient de dire et épeler son nom de famille sans prononcer son prénom. Une fois que vous avez le nom de famille, utilisez la fonction get_birth_date.",
            }
        )
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def get_birth_date(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        timing_config.TRANSCRIPTION_BUFFER_DELAY = 3
        self._temp_patient_data["last_name"] = args["last_name"]

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "confirm_new_patient_appt",
                        "description": "Utilise cette fonction une fois que tu as la date de naissance du patient",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "first_name": {
                                    "type": "string",
                                    "description": "Le prénom du patient",
                                },
                                "last_name": {
                                    "type": "string",
                                    "description": "Le nom de famille du patient",
                                },
                                "birth_date": {
                                    "type": "string",
                                    "description": "La date de naissance du patient au format DD/MM/YYYY",
                                },
                            },
                            "required": ["first_name", "last_name", "birth_date"],
                        },
                    },
                }
            ]
        )
        self._context.add_message(
            {
                "role": "system",
                "content": "Pour finir, demandez la date de naissance du patient sans parler du format et sans prononcer le prénom du patient et le nom du patient. Si tu ne comprends pas bien la date de naissance, explique que tu veux la date de naissance sous le format 20 Mars 1982 par exemple. Une fois que vous avez une date de naissance valide au bon format, utilisez la fonction confirm_new_patient_appt avec toutes les informations collectées.",
            }
        )
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def confirm_appt(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        if self._appointment_confirmed:
            return

        self._appointment_confirmed = True

        self._api_call_in_progress = True

        # Check if this is a double appointment
        is_double_appointment = "visit_motive_id_1" in self._appt_data

        if is_double_appointment:
            self._appt_data["visit_motive_id"] = self._appt_data["visit_motive_id_1"]
            self._appt_data["start_date"] = self._appt_data["steps"][0]["start_date"]
            self._appt_data["end_date"] = self._appt_data["steps"][0]["end_date"]

            api_url = "https://hook.eu1.make.com/th0lkdgn492srzybcypfc7d1m63g8voa"
            request_body = {
                "config": self._config,
                "client": self._patient_data["first_name"]
                + " "
                + self._patient_data["last_name"],
                "visit_motive_id": self._appt_data["visit_motive_id"],
                "type": self._appt_data["visit_motive_name"],
                "callID": self._callId,
                "number": self._caller_phone_number,
                "patient_id": self._patient_data["id"],
                "appt_id": self._appt_data.get("appt_id", None),
                "start_date": self._appt_data["start_date"],
                "end_date": self._appt_data["end_date"],
                "steps": self._appt_data["steps"],
                "address": self.configContent["address"],
            }
        else:
            api_url = "https://hook.eu1.make.com/xisj41nlix2srlizyrswfpjtepdrk7kd"
            request_body = {
                "config": self._config,
                "client": self._patient_data["first_name"]
                + " "
                + self._patient_data["last_name"],
                "equipment_agenda_id": self._appt_data["equipment_agenda_id"],
                "practitioner_agenda_id": self._appt_data["practitioner_agenda_id"],
                "visit_motive_id": self._appt_data["visit_motive_id"],
                "type": self._appt_data["visit_motive_name"],
                "agenda_id": self._appt_data["agenda_id"],
                "start_date": self._appt_data["start_date"],
                "end_date": self._appt_data["end_date"],
                "patient_id": self._patient_data["id"],
                "appt_id": self._appt_data.get("appt_id", None),
                "callID": self._callId,
                "number": self._caller_phone_number,
                "address": self.configContent["address"],
            }

            if self._appt_data.get("steps"):
                request_body["steps"] = self._appt_data["steps"]

        headers = {
            "Authorization": f"Bearer {self._api_token}",
            "Content-Type": "application/json",
        }
        await asyncio.to_thread(
            requests.post, api_url, json=request_body, headers=headers
        )

        self._api_call_in_progress = False

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "set_question",
                        "description": "Utilise cette fonction si le patient a d'autres questions",
                    },
                }
            ]
        )

        self._context.add_message(
            {
                "role": "system",
                "content": f"Parfait, je note ce rendez-vous dans l'agenda. {self._appt_data['after']} Vous allez recevoir un SMS de confirmation. Rendez-vous au {self.configContent['address']}. Tu ne peux pas changer la date et l'heure du rendez-vous ni prendre d'autre rendez-vous. Demande au patient s'il a d'autres questions.",
            }
        )

        if sounds["ding2.wav"] is not None:
            await llm.push_frame(sounds["ding2.wav"], FrameDirection.DOWNSTREAM)
        else:
            logger.warning("Ding sound not available, skipping audio cue")
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def confirm_new_patient_appt(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):
        if self._appointment_confirmed:
            return

        self._appointment_confirmed = True

        self._api_call_in_progress = True

        self._patient_data = {
            "first_name": args["first_name"],
            "last_name": args["last_name"],
            "birth_date": args["birth_date"],
        }

        # Check if this is a double appointment
        is_double_appointment = "visit_motive_id_1" in self._appt_data

        if is_double_appointment:
            self._appt_data["visit_motive_id"] = self._appt_data["visit_motive_id_1"]
            self._appt_data["start_date"] = self._appt_data["steps"][0]["start_date"]
            self._appt_data["end_date"] = self._appt_data["steps"][0]["end_date"]

            api_url = "https://hook.eu1.make.com/xjxsl14m4qjl5taxfjurom4sh3fuohdv"
            request_body = {
                "config": self._config,
                "first_name": args["first_name"],
                "last_name": args["last_name"],
                "birth_date": args["birth_date"],
                "visit_motive_id": self._appt_data["visit_motive_id"],
                "type": self._appt_data["visit_motive_name"],
                "callID": self._callId,
                "number": self._caller_phone_number,
                "steps": self._appt_data["steps"],
                "start_date": self._appt_data["start_date"],
                "end_date": self._appt_data["end_date"],
                "address": self.configContent["address"],
            }

        else:
            api_url = "https://hook.eu1.make.com/cqr3qproevntr3i8j1c78np16rk91s72"
            request_body = {
                "config": self._config,
                "first_name": args["first_name"],
                "last_name": args["last_name"],
                "birth_date": args["birth_date"],
                "visit_motive_id": self._appt_data["visit_motive_id"],
                "type": self._appt_data["visit_motive_name"],
                "practitioner_agenda_id": self._appt_data["practitioner_agenda_id"],
                "equipment_agenda_id": self._appt_data["equipment_agenda_id"],
                "agenda_id": self._appt_data["agenda_id"],
                "start_date": self._appt_data["start_date"],
                "end_date": self._appt_data["end_date"],
                "callID": self._callId,
                "number": self._caller_phone_number,
                "address": self.configContent["address"],
            }

        headers = {
            "Authorization": f"Bearer {self._api_token}",
            "Content-Type": "application/json",
        }
        response = await asyncio.to_thread(
            requests.post, api_url, json=request_body, headers=headers
        )

        response_data = response.json()

        if "id" in response_data:
            self._appt_data["appointment_set_id"] = response_data["id"]

        self._api_call_in_progress = False

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "set_question",
                        "description": "Utilise cette fonction si le patient a d'autres questions",
                    },
                }
            ]
        )

        self._context.add_message(
            {
                "role": "system",
                "content": f"Le rendez-vous a été réservé avec succès pour le nouveau patient. {self._appt_data['after']} Informez le patient que son rendez-vous est confirmé pour le {self._appt_data['start_date']}. Expliquez qu'ils recevront un SMS de confirmation. Tu ne peux pas changer la date et l'heure du rendez-vous ni prendre d'autre rendez-vous. Demande au patient s'il a d'autres questions.",
            }
        )

        if sounds["ding2.wav"] is not None:
            await llm.push_frame(sounds["ding2.wav"], FrameDirection.DOWNSTREAM)
        else:
            logger.warning("Ding sound not available, skipping audio cue")
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def cancel_appt(
        self, function_name, tool_call_id, args, llm, context, result_callback
    ):

        self._api_call_in_progress = True

        api_url = "https://doctolib-secret-scrape-global.onrender.com/api/v1/no-show"
        request_body = {
            "config": self._config,
            "id": self._appt_data["appt_id"],
        }
        headers = {
            "Authorization": f"Bearer {self._api_token}",
            "Content-Type": "application/json",
        }
        await asyncio.to_thread(
            requests.put, api_url, json=request_body, headers=headers
        )

        self._api_call_in_progress = False

        self._context.set_tools(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "ask_date",
                        "description": "Utilise cette fonction si le patient a besoin d'un autre rendez-vous",
                    },
                }
            ]
        )

        self._context.add_message(
            {
                "role": "system",
                "content": "Le rendez-vous a été annulé avec succès. Informez le patient que son rendez-vous a été annulé. Demandez-lui s'il a besoin d'un autre rendez-vous. Si oui utilise la fonction ask_date. Sinon dites au revoir poliment.",
            }
        )

        if sounds["ding2.wav"] is not None:
            await llm.push_frame(sounds["ding2.wav"], FrameDirection.DOWNSTREAM)
        else:
            logger.warning("Ding sound not available, skipping audio cue")
        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )

    async def appt_confirm(
        self, function_name, tool_call_id, args, llm, context, result_callback, intent
    ):

        if "appt_id" not in self._appt_data or self._appt_data.get("appt_id") is None:
            if not self._patient_data.get("appt_id"):
                await self._tts.say("Je n'ai pas trouvé le rendez-vous à " + intent)
                await self.set_question(
                    function_name,
                    tool_call_id,
                    {"new_patient": False},
                    llm,
                    context,
                    result_callback,
                )
                return

        self._appt_data["appt_id"] = self._patient_data["appt_id"]
        self._appt_data["visit_motive_id"] = self._patient_data["visit_motive_id"]
        self._appt_data["visit_motive_name"] = self._patient_data["visit_motive_name"]
        self._appt_data["agenda_id"] = self._patient_data["agenda_id"]
        self._appt_data["instructions"] = ""
        self._appt_data["price"] = ""
        self._appt_data["after"] = ""
        self._appt_data["before"] = ""

        if intent == "modifier":
            self._context.set_tools(
                [
                    {
                        "type": "function",
                        "function": {
                            "name": "ask_date",
                            "description": "Utilise cette fonction si c'est le bon rendez-vous à modifier",
                        },
                    },
                    {
                        "type": "function",
                        "function": {
                            "name": "set_question",
                            "description": "utilise cette fonction si ce n'est pas le bon rendez-vous",
                        },
                    },
                ]
            )
            self._context.add_message(
                {
                    "role": "system",
                    "content": f"Vérifie avec le patient si le rendez-vous à modifier est bien cette consultation {self._patient_data['visit_motive_name']} à cette date : {self._patient_data['appt_date']}. Si oui utilise la fonction ask_date. Sinon utilise la fonction set_question.",
                }
            )

        elif intent == "annuler":
            self._context.set_tools(
                [
                    {
                        "type": "function",
                        "function": {
                            "name": "cancel_appt",
                            "description": "Utilise cette fonction si c'est le bon rendez-vous à annuler",
                        },
                    },
                    {
                        "type": "function",
                        "function": {
                            "name": "set_question",
                            "description": "utilise cette fonction si ce n'est pas le bon rendez-vous",
                        },
                    },
                ]
            )
            self._context.add_message(
                {
                    "role": "system",
                    "content": f"Vérifie avec le patient si le rendez-vous à annuler est bien cette consultation {self._patient_data['visit_motive_name']} à cette date : {self._patient_data['appt_date']}. Si oui utilise la fonction cancel_appt. Sinon utilise la fonction set_question.",
                }
            )

        await llm.process_frame(
            OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM
        )
