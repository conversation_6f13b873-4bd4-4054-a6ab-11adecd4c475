from models import CompanyData, PatientData
from nodes.base.prompts import Base<PERSON>rompts
from nodes.appointment_form.prompts import AppointmentFormPrompts
from nodes.contact_form.prompts import ContactFormPrompts
from .constant_specific import SPECIALITIES_NAMES
import random
from typing import Any, Optional
from .tools import EDLTools


class EdlPrompts(BasePrompts):
    def __init__(self, company_data: CompanyData):
        self.company_data = company_data
        self.appointment_form_prompt = AppointmentFormPrompts(self.company_data)
        self.contact_form_prompt = ContactFormPrompts(self.company_data)
        self.specialities = SPECIALITIES_NAMES.get(self.company_data.config, [])
        self.tools = EDLTools(self.specialities)

    def _set_message(self, message: str):
        message = " ".join(
            [line.strip() for line in message.split("\n") if line.strip()]
        )
        return {"role": "system", "content": message}

    def ask_speciality(
        self,
        patient_data: PatientData = None,
        return_node: Optional[dict[str, Any]] = None,
    ):
        return_node_name = return_node["function"]["name"] if return_node else ""

        all_specialities = self.specialities
        specialities_to_ask = self.specialities

        if patient_data and patient_data.historical_doctors:
            specialitiy_id_doctors = [
                speciality for speciality in patient_data.historical_doctors.keys()
            ]
            specialities = self.company_data.inbound_config_file.specialities
            list_of_specialities_patient = [
                speciality
                for speciality in specialities
                if speciality.id in specialitiy_id_doctors
            ]

            specialities_to_ask = [
                speciality.name for speciality in list_of_specialities_patient
            ]

        def join_with_ou(items):
            if not items:
                return ""
            if len(items) == 1:
                return items[0]
            return ", ".join(items[:-1]) + " ou bien " + items[-1]

        all_specialities_str = join_with_ou(
            [speciality for speciality in all_specialities]
        )
        specialities_to_ask_str = join_with_ou(
            [speciality for speciality in specialities_to_ask]
        )

        msg = f"Dit: Pour quelle spécialité appelez-vous : {specialities_to_ask_str} ?"

        if len(specialities_to_ask) > 7:
            # sélectionne aléatoirement 5 spécialités de la liste
            random_speciality = random.sample(specialities_to_ask, 5)
            specialities_to_ask_str = join_with_ou(
                [speciality for speciality in random_speciality]
            )

            msg = f"'Dit: Pour quelle spécialité appelez-vous ? Par exemple dites: {specialities_to_ask_str} ?'"

        if len(specialities_to_ask) < 3:
            other_specialities = [
                speciality
                for speciality in all_specialities
                if speciality not in specialities_to_ask
            ]
            specialities_to_ask += other_specialities[:3]
            specialities_to_ask_str = join_with_ou(
                [speciality for speciality in specialities_to_ask]
            )
            msg = f"Dit: Pour quelle spécialité appelez-vous ? Par exemple dites: {specialities_to_ask_str} ?"

        message = self._set_message(
            f"""
                {msg}
                --
                Pour ton information les spécialités possibles sont : {all_specialities_str}
                Si tu as compris la spécialité, utilise la fonction 'handle_speciality' pour continuer.
                Si l'utilisateur veut être transféré, utilise la fonction 'forward_call'.
                {"Si le patient souhaite retourner à la question précédente, utilise la fonction "+return_node_name if return_node else ""}
                """
        )

        tools = [self.tools.handle_speciality, self.tools.forward_call]

        return_node and tools.append(return_node)

        return message, tools

    def ask_how_many_motives(self):
        """
        Ask the user how many motives they have for the appointment.
        """
        message = self._set_message(
            """
            Dit: 'Souhaitez-vous un seul ou deux examens ?'
            --
            utilise la fonction 'handle_how_many_motives' pour continuer.
          """
        )

        tools = [self.tools.handle_how_many_motives]
        return message, tools
