from typing import Optional, Any
from models import CompanyData, PatientData
from .tools import SomedTools
from nodes.base.prompts import BasePrompts
from nodes.contact_form.prompts import ContactFormPrompts
from nodes.appointment_form.prompts import AppointmentFormPrompts
from .constants_somed import SPECIALITIES_NAMES
import random


class SomedPrompts(BasePrompts):
    def __init__(self, company_data: CompanyData):
        self.company_data = company_data
        self.contact_form_prompts = ContactFormPrompts(company_data)
        self.appointment_form_prompts = AppointmentFormPrompts(company_data)
        self.specialities = SPECIALITIES_NAMES.get(company_data.config, [])
        self.tools = SomedTools(self.specialities)

    def ask_speciality(
        self,
        patient_data: PatientData = None,
        return_node: Optional[dict[str, Any]] = None,
    ):
        return_node_name = return_node["function"]["name"] if return_node else ""

        all_specialities = self.specialities
        specialities_to_ask = self.specialities

        if patient_data and patient_data.historical_doctors:
            specialitiy_id_doctors = [
                speciality for speciality in patient_data.historical_doctors.keys()
            ]
            specialities = self.company_data.inbound_config_file.specialities
            list_of_specialities_patient = [
                speciality
                for speciality in specialities
                if speciality.id in specialitiy_id_doctors
            ]

            specialities_to_ask = [
                speciality.name for speciality in list_of_specialities_patient
            ]

        all_specialities_str = ", ".join(
            [speciality for speciality in all_specialities]
        )
        specialities_to_ask_str = ", ".join(
            [speciality for speciality in specialities_to_ask]
        )

        specialities_to_ask_str = (
            specialities_to_ask_str.lower()
            .replace("chirurgien-dentiste", "dentisterie, orthodontie")
            .replace("médecin généraliste", "genéraliste")
        )

        all_specialities_str = (
            all_specialities_str.lower()
            .replace("chirurgien-dentiste", "dentisterie, orthodontie")
            .replace("médecin généraliste", "genéraliste")
        )

        msg = f"Dit: Pour quelle spécialité appelez-vous : {specialities_to_ask_str} ?"

        if len(specialities_to_ask) > 7:
            # randomly select 5 specialities from the list
            random_speciality = random.sample(specialities_to_ask, 5)
            specialities_to_ask_str = ", ".join(
                [speciality for speciality in random_speciality]
            )

            msg = f"'Dit: Pour quelle spécialité appelez-vous ? Par exemple dites: {specialities_to_ask_str} ?'"

        if len(specialities_to_ask) < 3:
            other_specialities = [
                speciality
                for speciality in all_specialities
                if speciality not in specialities_to_ask
            ]
            specialities_to_ask += other_specialities[:3]
            specialities_to_ask_str = ", ".join(
                [speciality for speciality in specialities_to_ask]
            )
            msg = f"Dit: Pour quelle spécialité appelez-vous ? Par exemple dites: {specialities_to_ask_str} ?"

        words_boost_str = ""
        words_boost = self.company_data.bot_configuration.words_boost_prompt
        words_boost_arr = (
            [
                f"- Le mot '{word_said}' devrait être '{actual_word}'"
                for word_said, actual_word in words_boost.items()
            ]
            if words_boost
            else []
        )
        if words_boost:
            words_boost_str = "Situation particulière :\n"
            words_boost_str += (
                "\n".join(map(str, words_boost_arr)) if words_boost_arr else ""
            )

        message = self._set_message(
            f"""
                {msg}
                --
                Si tu as compris la spécialité, utilise la fonction 'handle_speciality' pour continuer.
                Si l'utilisateur veut être transféré, utilise la fonction 'forward_call'.
                {"Si le patient souhaite retourner à la question précédente, utilise la fonction "+return_node_name if return_node else ""}
                **NE DIS PAS TOUTES LES SPECIALITES, juste ce donné avant le --**:
                """
        )

        tools = [self.tools.handle_speciality, self.tools.forward_call]

        return_node and tools.append(return_node)

        return message, tools
