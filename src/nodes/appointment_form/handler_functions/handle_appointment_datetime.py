from typing import TYPE_CHECKING
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo

if TYPE_CHECKING:
    from appointment_form.intake_processor import AppointmentFormIntakeProcessor

from pipecat.services.openai.llm import OpenAILLMService, OpenAILLMContext
from models import BookingProviderType
from constants import Intents
from ..node_functions import get_next_availabilties, find_motive_by_name
import utils.appointments
from loguru import logger
from ..tools import AppointmentFormTools


async def handle_appointment_datetime(
    self: "AppointmentFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):

    def normalize_datetime(dt_str: str):
        dt_str = dt_str.replace("T", " ")
        if dt_str.count(":") == 2:
            dt_str = ":".join(dt_str.split(":")[:2])
        return dt_str

    def parse_datetime(dt_str: str):
        try:
            return datetime.strptime(dt_str, "%Y-%m-%d %H:%M").replace(
                tzinfo=ZoneInfo("Europe/Paris")
            )
        except Exception:
            return None

    def format_dt(dt: datetime):
        return utils.appointments.format_datetime_with_day_in_french(dt).split("(")[0]

    def to_paris(dt: datetime):  # Helper to ensure all datetimes are in Paris timezone
        return dt.astimezone(ZoneInfo("Europe/Paris"))

    async def fetch_and_respond_with_appointments(base_dt: datetime, lookback_days=0):
        check_dt = base_dt - timedelta(days=lookback_days) if lookback_days else base_dt
        self.next_availabilities = await get_next_availabilties(
            check_dt,
            self.next_availabilities,
            self.booking_provider,
            self.appointment_form,
            self.agenda_ids_to_check,
            self.company_data,
            self._patient_data,
            self.phone_caller,
        )
        return utils.appointments.get_closest_appointments(
            self.next_availabilities,
            base_dt,
            False,
            72,
            blacklist_appointment=self.black_list_appointments,
        )

    wish_dt = args.get("appointment_datetime")
    wish_day = args.get("appointment_day")
    logger.info(args)

    if wish_day and not wish_dt:
        wish_dt = utils.appointments.get_date_from_day(wish_day)

    # Parse wish_day as fallback if wish_dt is not provided
    if not wish_dt and wish_day and isinstance(wish_day, str) and "-" in wish_day:
        try:
            wish_dt = datetime.strptime(wish_day, "%Y-%m-%d").replace(
                hour=0, minute=0, tzinfo=ZoneInfo("Europe/Paris")
            )
        except ValueError:
            logger.error("Invalid format for wish_day.")

    # Parse simple YYYY-MM-DD dates early (consolidating all date parsing logic)
    if (
        wish_dt
        and isinstance(wish_dt, str)
        and ":" not in wish_dt
        and "-" in wish_dt
        and wish_dt.count("-") == 2
        and wish_dt not in ["autre", "aucun", "non", ""]
    ):
        try:
            wish_dt = datetime.strptime(wish_dt, "%Y-%m-%d").replace(
                hour=0, minute=0, tzinfo=ZoneInfo("Europe/Paris")
            )
        except ValueError:
            logger.error("Invalid format for appointment_datetime.")

    half_day_flag = args.get("with_half_matin_aprem", False)
    with_doctor = self.say_name_doctor

    # Handle blacklisted dates - mark them as rejected instead of converting back to string
    if isinstance(wish_dt, datetime) and wish_dt.hour == 0 and wish_dt.minute == 0:
        if wish_dt in self.black_list_appointments:
            # Mark as rejected by treating it like a rejection value
            wish_dt = "non"
        else:
            self.black_list_appointments.append(wish_dt)

    # Handle rejection values and invalid dates
    if wish_dt in [None, "", "autre", "aucun", "non"] or (
        isinstance(wish_dt, str)
        and not utils.appointments.is_valid_datetime(wish_dt, "%Y-%m-%d %H:%M")
    ):
        if self.suggested_appointments:
            self.black_list_appointments += self.suggested_appointments

        await self._reply_to_user(
            result_callback,
            context,
            llm,
            "Dites-moi simplement à quel moment vous seriez disponible, je vais m'adapter !",
            [AppointmentFormTools.handle_appointment_datetime],
        )
        return

    # Normalize and parse datetime if still a string (removing redundant date parsing)
    # self.suggested_appointments = []
    self.black_list_appointments = self.suggested_appointments

    if isinstance(wish_dt, str):
        wish_dt = normalize_datetime(wish_dt)
        if not utils.appointments.is_valid_datetime(wish_dt, "%Y-%m-%d %H:%M"):
            prompt, tools = self.prompts.suggest_appointment_datetime(
                [],
                with_doctor=with_doctor,
                first_sentence="Je n'ai pas compris la date de rendez-vous souhaitée.",
            )
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return
        wish_dt = parse_datetime(wish_dt)

    if not isinstance(wish_dt, datetime):
        await self._reply_to_user(
            result_callback,
            context,
            llm,
            "Je n'ai pas compris la date de rendez-vous souhaitée.",
            [AppointmentFormTools.handle_appointment_datetime],
        )
        return

    dt_str = format_dt(wish_dt)

    is_same_day = any(
        to_paris(a.start_date).date() == wish_dt.date()
        for a in self.next_availabilities
    )

    if not is_same_day:
        message = (
            f"Je vérifie nos disponibilités les plus proches du {dt_str}."
            if not half_day_flag
            else f"Je vérifie nos disponibilités les plus proches du {dt_str.split(' ')[0]} {'matin' if wish_dt.hour < 12 else 'après-midi'} {' '.join(dt_str.split(' ')[1:])}."
        )
        await self._say_and_wait(llm, context, message)
        self.next_availabilities = await get_next_availabilties(
            wish_dt,
            self.next_availabilities,
            self.booking_provider,
            self.appointment_form,
            self.agenda_ids_to_check,
            self.company_data,
            self._patient_data,
            self.phone_caller,
        )

    logger.bind(next_availabilities=self.next_availabilities, blacklist=self.black_list_appointments)\
        .info(f"Get closest appointments for {wish_dt}")

    slots = utils.appointments.get_closest_appointments(
        self.next_availabilities,
        wish_dt,
        True,
        72,
        blacklist_appointment=self.black_list_appointments,
    )

    # check for other doctors if no slots for specific doctor
    if (
        not slots
        and self.appointment_form.medecin
        and self.appointment_form.agenda_id
        and not self.has_asked_to_change_doctor
    ):
        doctor = await self.booking_provider.get_doctor_by_agenda_id(
            self.appointment_form.agenda_id
        )
        if doctor:
            prompt, tools = self.prompts.ask_continue_with_same_doctor(doctor)
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

    if not slots:
        await self._say_and_wait(
            llm,
            context,
            f"Il n'y a pas de créneaux disponibles pour le {dt_str}",
            no_wait=True,
        )

        slots = utils.appointments.get_closest_appointments(
            self.next_availabilities,
            wish_dt,
            False,
            72,
            blacklist_appointment=self.black_list_appointments,
        )

        if not slots:
            is_next_day_avail = any(
                to_paris(a.start_date).date() == (wish_dt + timedelta(days=1)).date()
                for a in self.next_availabilities
            )

            if not is_next_day_avail:
                slots += await fetch_and_respond_with_appointments(
                    wish_dt + timedelta(days=1)
                )

                now = datetime.now(ZoneInfo("Europe/Paris"))
                before_dt = wish_dt - timedelta(days=4)
                # Ensure before_dt is timezone-aware
                if before_dt.tzinfo is None:
                    before_dt = before_dt.replace(tzinfo=ZoneInfo("Europe/Paris"))

                if before_dt > now + timedelta(days=1):
                    slots += await fetch_and_respond_with_appointments(before_dt)

                slots += utils.appointments.get_closest_appointments(
                    self.next_availabilities,
                    wish_dt,
                    False,
                    94,
                    self.black_list_appointments,
                )

        slots = utils.appointments.get_different_time_slots(
            slots or [], 4, 2
        ) or utils.appointments.get_different_time_slots(self.next_availabilities, 2, 2)

        prompt, tools = self.prompts.suggest_appointment_datetime(
            slots, with_doctor=with_doctor
        )
        self.suggested_appointments = slots
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    # Filter appointments based on time preference
    if half_day_flag:
        slots = utils.appointments.get_appointments_in_half_day(
            self.next_availabilities, wish_dt
        )
    elif (wish_dt.hour == 0 and wish_dt.minute == 0) or wish_day:
        slots = utils.appointments.get_appointments_on_day(
            self.next_availabilities, wish_dt, self.black_list_appointments
        )
    else:
        slots = utils.appointments.get_closest_appointments(
            self.next_availabilities,
            wish_dt,
            blacklist_appointment=self.black_list_appointments,
        )

    if not slots:
        slots = self.next_availabilities

    if len(slots) > 1:

        if wish_dt.hour == 0 and wish_dt.minute == 0:
            # remove all dates before wish_dt
            wish_dt_paris = (
                to_paris(wish_dt)
                if wish_dt.tzinfo is None or wish_dt.tzinfo.utcoffset(wish_dt) is None
                else wish_dt
            )
            slots = [
                slot for slot in slots if to_paris(slot.start_date) >= wish_dt_paris
            ]

        slots = utils.appointments.get_different_time_slots(
            slots, 4, 2
        ) or utils.appointments.get_different_time_slots(slots, 2, 2)
        prompt, tools = self.prompts.suggest_appointment_datetime(
            slots, with_doctor=with_doctor
        )
        self.suggested_appointments = slots
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    ## SLOT has been found, ask for confirmation
    final_slot = slots[0]

    if to_paris(final_slot.start_date) != wish_dt:
        prompt, tools = self.prompts.suggest_appointment_datetime(
            [final_slot], with_doctor=with_doctor
        )
        self.suggested_appointments = slots
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    # Fill the appointment form
    if (
        not self.appointment_form.medecin
        and self.company_data.bot_configuration.ask_name_doctor
    ):
        motive_name = self.appointment_form.visit_motive_name.replace(
            "(tous)", ""
        ).strip()
        motive = find_motive_by_name(
            motive_name, self.company_data, doctor_name=final_slot.medecin
        )
        if not motive:
            self.phone_caller.has_note = True
            await self.end_call(
                "end_call", None, {}, llm, context, result_callback, True
            )
            self.n8n_client.send_error_server(
                self.company_data.config,
                self.phone_caller,
                "Error: No motive found for the appointment with doctor and motive alias",
            )
            return

        self.appointment_form.visit_motive_id = motive.visit_motive_id
        self.appointment_form.type = motive.type
        self.appointment_form.speciality_id = motive.speciality_id
        self.appointment_form.visit_motive_name = motive.visit_motive_name

    self.appointment_form.start_date = final_slot.start_date
    self.appointment_form.end_date = final_slot.end_date
    self.appointment_form.agenda_id = final_slot.agenda_id
    self.appointment_form.modalite_id = final_slot.modalite_id
    self.appointment_form.equipment_agenda_id = final_slot.equipment_agenda_id
    self.appointment_form.steps = final_slot.steps or []

    if final_slot.visit_motive_id:
        self.appointment_form.visit_motive_id = final_slot.visit_motive_id

    if self.company_data.booking_provider != BookingProviderType.EDL:
        self.appointment_form.medecin = final_slot.medecin

    self.has_confirm_datetime = True

    if (
        self.motives_ids_with_reasons
        and self.phone_caller.intent == Intents.NOUVEAU
        and int(self.appointment_form.visit_motive_id) in self.motives_ids_with_reasons
    ):
        prompt, tools = self.prompts.ask_more_information_for_motive(
            self.appointment_form
        )
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    if (
        self.company_data.config == "config77"
        and self.appointment_form.visit_motive_id == 1656986
    ):
        await self._say_and_wait(
            llm,
            context,
            "Si vous portez des lentilles souples, enlevez-les 24 heures avant la consultation. ",
        )

    if not self._patient_data.has_completed_form():
        prompt, tools = self.prompts.contact_form_prompts.ask_first_name()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    if self.phone_caller.intent in [Intents.MODIFIER, Intents.ANNULATION]:
        prompt, tools = self.prompts.ask_reason_of_cancellation(
            self.phone_caller.intent
        )
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    prompt, tools = self.prompts.ask_appointment_confirmation(
        self.appointment_form, self.phone_caller.intent.value
    )
    await self._reply_to_user(result_callback, context, llm, prompt, tools)
