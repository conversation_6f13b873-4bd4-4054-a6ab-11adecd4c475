from loguru import logger
from pipecat.services.openai.llm import OpenAILLMContext, OpenAILLMService
from nodes.base.intake_processor import BaseIntakeProcessor
from models import CompanyData, PatientData, PhoneCaller
import utils.time
from .prompts import CaedaPrompts
from datetime import datetime, timedelta
from .constants_specific import (
    MOTIVE,
    ANESTHESIA_AGENDA_MOTIVE_IDS,
    TEST_DEPISTAGE_ADECA_ID,
    MANOMETRIE_AND_PH_ID,
    PH_METRIE_ID,
    ANNE_DERLON_ID,
    MAX_RETARD_BEFORE_TASK,
    SE_PRACTICE_ID,
    ALL_DOCTORS_CONSULTATION_MOTIVE_ID,
)
from .tools import CaedaTools

from lib.doctolib_client import DoctolibClient
import utils
import utils.appointments
import utils.tts
import utils.date
from constants import Intents
from .nodes_functions import (
    cancel_appointment,
    get_doctor_by_name,
    modify_appointment,
)
from locales import get_locale

phrases = get_locale()


class IntakeProcessor(BaseIntakeProcessor):
    def __init__(
        self,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        company_data: CompanyData,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
    ):
        self.examen_date_for_anhestesia = None
        self.doctolib_client = DoctolibClient(company_data)
        super().__init__(company_data, phone_caller, patient_data)

        self.prompts = CaedaPrompts(company_data, llm)
        prompt, tools = self.prompts.init()
        context.set_tools(tools)
        context.add_message(prompt)

    def __init_nodes__(self):

        functions = {}

        # Add functions from the current class
        for function_name in dir(self.__class__):
            if callable(
                getattr(self.__class__, function_name)
            ) and not function_name.startswith("_"):
                functions[function_name] = self

        return list(functions.items())

    async def handle_person_type(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        person_type = args["person_type"]
        if person_type == "patient":
            prompt, tools = self.prompts.init_patient()
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        await self.forward_call(
            "forward_call", None, args, llm, context, result_callback
        )
        return

    """
    Check Intent of the call
    """

    async def handle_check_intent(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        intent = self.INTENT_LOOKUP.get(args.get("intent"))

        if not intent:
            message = "Désolé, je ne comprends pas votre demande. Veuillez reformuler votre demande."
            await self._reply_to_user(result_callback, context, llm, message)
            return

        intent_script = utils.tts.get_intent_sentence(intent)
        await self._say_and_wait(
            llm,
            context,
            message=intent_script
        )
        self.phone_caller.intent = intent

        if intent == Intents.NOUVEAU and self._patient_data.bounced_at:
            await self._say_and_wait(
                llm,
                context,
                "Je suis désolé, vous ne pouvez pas prendre un rendez-vous pour le moment. Je vais laisser un message à notre centre médical.",
            )
            self.phone_caller.successful_call = True
            self.phone_caller.has_note = True
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        if intent == Intents.QUESTION:
            self.phone_caller.has_note = True

        if intent in [
            Intents.NOUVEAU,
            Intents.ANNULATION,
            Intents.CONFIRMER,
            Intents.QUESTION,
        ]:
            if self._patient_data.is_new_patient:
                prompt, tools = await self.prompts.ask_first_name(
                    return_node=CaedaTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=CaedaTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return
        elif intent in [Intents.MODIFIER, Intents.RETARD]:
            if self._patient_data.is_new_patient:
                await self.forward_call(
                    "forward_call", None, args, llm, context, result_callback
                )
            else:
                prompt, tools = self.prompts.ask_confirm_identity(
                    self._patient_data, return_node=CaedaTools.handle_check_intent
                )
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif Intents.URGENCE == intent:
            await self.forward_call(
                "forward_call", None, args, llm, context, result_callback
            )
        else:
            message = "Désolé, je ne comprends pas votre demande. Veuillez reformuler votre demande."
            await self._reply_to_user(result_callback, context, llm, message)
            return

    async def handle_confirm_identity(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        confirm = args["confirm"]
        confirm = confirm == True or "oui" in str(confirm).lower()

        if not confirm:
            self._patient_data = PatientData(is_new_patient=True)

        if Intents.NOUVEAU == self.phone_caller.intent:
            if self._patient_data.is_new_patient:
                prompt, tools = await self.prompts.ask_first_name()
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return
            else:
                if utils.date.is_adult(self._patient_data.birthdate):
                    prompt, tools = self.prompts.ask_motive()
                    await self._reply_to_user(
                        result_callback, context, llm, prompt, tools
                    )
                    return
                else:
                    if self._patient_data.birthdate == None:
                        prompt, tools = self.prompts.ask_birthdate_of_patient()
                        await self._reply_to_user(
                            result_callback, context, prompt, tools
                        )
                        return
                    else:
                        await self._say_and_wait(
                            llm,
                            context,
                            "Je suis désolé, le patient doit être majeur pour prendre un rendez-vous.",
                        )
                        await self.end_call(
                            "end_call", None, args, llm, context, result_callback
                        )
                        return

        elif Intents.ANNULATION == self.phone_caller.intent:
            self.appointment_form = self._patient_data.next_appointment
            if self._patient_data.is_new_patient:
                prompt, tools = await self.prompts.ask_first_name()
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return
            else:
                if (
                    self.appointment_form.type == MOTIVE.ANESTHESIE.value
                    or self.appointment_form.type == MOTIVE.EXAMEN.value
                ):
                    await self.forward_call(
                        "forward_call", None, args, llm, context, result_callback
                    )
                    return

                TWO_DAYS = 2880
                if utils.appointments.is_appointment_soon(
                    self.appointment_form, TWO_DAYS
                ):
                    await self.forward_call(
                        "forward_call", None, args, llm, context, result_callback
                    )
                    return

                prompt, tools = self.prompts.ask_reason_of_cancellation()
                if self.appointment_form.type == MOTIVE.EXAMEN.value:
                    self.phone_caller.has_note = True
                    self.appointment_form.practitioner_id = SE_PRACTICE_ID

                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif Intents.MODIFIER == self.phone_caller.intent:
            if self._patient_data.is_new_patient:
                await self.forward_call(
                    "forward_call", None, args, llm, context, result_callback
                )
            else:
                if self._patient_data.next_appointment:
                    self.appointment_form.visit_motive_id = (
                        self._patient_data.next_appointment.visit_motive_id
                    )
                    self.appointment_form.agenda_id = (
                        self._patient_data.next_appointment.agenda_id
                    )
                    self.appointment_form.type = (
                        self._patient_data.next_appointment.type
                    )

                    if self.appointment_form.type == MOTIVE.CONSULTATION.value:
                        str_date = utils.appointments.format_appointment_to_datetime_with_day_in_french(
                            self._patient_data.next_appointment
                        ).split(
                            "("
                        )[
                            0
                        ]
                        await self._say_and_wait(
                            llm,
                            context,
                            f"J'ai trouvé votre rendez-vous du {str_date}.",
                        )

                        prompt, tools = self.prompts.ask_reason_of_cancellation(
                            self.phone_caller.intent
                        )
                        await self._reply_to_user(
                            result_callback, context, llm, prompt, tools
                        )
                        return

                self.phone_caller.has_note = True
                await self._say_and_wait(
                    llm,
                    context,
                    "Je notifie le centre médical de votre demande de modification.",
                )
                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return

        elif Intents.RETARD == self.phone_caller.intent:
            if self._patient_data.is_new_patient:
                await self.forward_call(
                    "forward_call", None, args, llm, context, result_callback
                )
                return
            else:
                prompt, tools = self.prompts.ask_how_long_late()
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif Intents.URGENCE == self.phone_caller.intent:
            self.phone_caller.has_note = True
            await self._say_and_wait(
                llm,
                context,
                "Je notifie le centre médical, il vous recontactera au plus vite",
            )
            await self.end_call("end_call", None, args, llm, context, result_callback)

        elif Intents.QUESTION == self.phone_caller.intent:
            self.phone_caller.has_note = True
            if self._patient_data.is_new_patient:
                prompt, tools = await self.prompts.ask_first_name()
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return
            else:
                prompt, tools = self.prompts.ask_question(self._patient_data)
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

        elif Intents.CONFIRMER == self.phone_caller.intent:
            if self._patient_data.is_new_patient:
                prompt, tools = self.prompts.ask_first_name()
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return
            else:
                self.phone_caller.has_note = True
                await self._say_and_wait(
                    llm,
                    context,
                    "Je notifie le centre médical de votre demande.",
                )
                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return

    async def handle_first_name(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        first_name = args["first_name"]

        if self.phone_caller.intent != Intents.NOUVEAU:
            self.phone_caller.has_note = True

        if first_name:
            self._patient_data.first_name = first_name
            prompt, tools = self.prompts.ask_last_name()
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        await self._reply_to_user(
            result_callback,
            "Désolé, je n'ai pas compris votre prénom. Pouvez-vous répéter?",
        )
        return

    async def handle_last_name(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        last_name = args["last_name"]

        if last_name:
            self._patient_data.last_name = last_name
            prompt, tools = self.prompts.ask_birthdate_of_patient()
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        await self._reply_to_user(
            result_callback,
            "Désolé, je n'ai pas compris votre nom. Pouvez-vous répéter?",
        )
        return

    async def handle_birthdate_of_patient(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        birthdate = args["birthdate"]

        if not utils.appointments.is_valid_date(birthdate):
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                "Désolé, je n'ai pas compris votre âge. Pouvez-vous répéter?",
            )
        birthdate = datetime.strptime(birthdate, "%Y-%m-%d")
        self._patient_data.birthdate = birthdate
        if self.phone_caller.intent == Intents.NOUVEAU:
            if not utils.date.is_adult(birthdate):
                self.retries_take_appointment += 1
                if self.retries_take_appointment > 3:
                    await self._say_and_wait(
                        llm,
                        context,
                        "Je suis désolé, vle patient doit être majeur pour prendre un rendez-vous.",
                    )
                    await self.end_call(
                        "end_call", None, args, llm, context, result_callback
                    )
                    return

                await self._reply_to_user(
                    result_callback,
                    context,
                    llm,
                    "Je ne suis pas sûr d'avoir bien compris. Pouvez-vous me donner votre date de naissance?",
                )
                return

            prompt, tools = self.prompts.ask_motive()
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        if self.phone_caller.intent == Intents.MODIFIER:
            await self._say_and_wait(
                llm,
                context,
                "Je notifie le centre médical de votre demande.",
            )
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        if self.phone_caller.intent == Intents.ANNULATION:
            prompt, tools = self.prompts.ask_reason_of_cancellation()
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        if self.phone_caller.intent == Intents.QUESTION:
            prompt, tools = self.prompts.ask_question(self._patient_data)
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        if self.phone_caller.intent == Intents.CONFIRMER:
            await self._say_and_wait(
                llm,
                context,
                "Je notifie le centre médical que vous souhaitez confirmer votre rendez-vous",
            )
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        await self._reply_to_user(
            result_callback,
            context,
            "Désolé, je n'ai pas compris votre âge. Pouvez-vous répéter?",
        )

    """
    Annulation
    """

    async def handle_reason_of_cancellation(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        reason = args["reason"]
        self.appointment_form.notes = reason

        if not reason:
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                "Désolé, je n'ai pas compris votre raison. Pouvez-vous répéter?",
            )

        if self._patient_data.is_new_patient or not self._patient_data.next_appointment:
            await self._say_and_wait(
                llm,
                context,
                "Je notifie le centre médical que vous souhaitez annuler un rendez-vous.",
            )
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        if self.phone_caller.intent == Intents.MODIFIER:
            self._patient_data.next_appointment.notes = reason

            self.next_availabilities = (
                await self.doctolib_client.get_next_availabilities(
                    self.appointment_form,
                    [str(self.appointment_form.agenda_id)],
                    min_time_difference=3,
                    number_of_time_slots=3,
                )
            )

            if not self.next_availabilities:
                await self._say_and_wait(
                    llm,
                    context,
                    "Je suis désolé, il n'y a pas de disponibilité pour le moment. Je notifie le centre médical de votre demande.",
                )
                self.phone_caller.has_note = True
                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return

            prompt, tools = self.prompts.suggest_appointment_datetime(
                self.next_availabilities[:2]
            )

            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        prompt, tools = self.prompts.ask_confirm_appointment(
            self.appointment_form, self.phone_caller.intent.value
        )
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    async def handle_appointment_confirmation(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        confirm = args["confirm"]
        confirm = confirm == True or "oui" in str(confirm).lower()

        if self.phone_caller.intent == Intents.ANNULATION:
            if confirm:
                await cancel_appointment(
                    self.doctolib_client,
                    self.appointment_form,
                    self._patient_data.appointments,
                )
                await llm.push_frame(self.sounds["ding2.wav"])
                await self._say_and_wait(
                    llm,
                    context,
                    "Votre rendez-vous est annulé.",
                )
                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return
            else:
                self.phone_caller.has_note = True
                await self._say_and_wait(
                    llm,
                    context,
                    "Je notifie le centre médical que vous souhaitez annuler un rendez-vous.",
                )
                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return

        if self.phone_caller.intent == Intents.NOUVEAU:
            if confirm:
                if not self.appointment_form.is_booking_created:
                    self.appointment_form.is_booking_created = True
                    self.appointment_form = (
                        await self.doctolib_client.create_new_booking(
                            self.phone_caller,
                            self._patient_data,
                            self.appointment_form,
                        )
                    )

                intent = utils.tts.get_intent_description(self.phone_caller.intent)
                self.phone_caller.successful_call = True
                await llm.push_frame(self.sounds["ding2.wav"])
                instruction_msg = (
                    self.appointment_form.instructions
                    if self.appointment_form.instructions
                    else ""
                )

                await self._say_and_wait(
                    llm=llm,
                    context=context,
                    message=f"Votre {intent} est confirmé. {instruction_msg}",
                )

                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return
            else:
                await self._say_and_wait(
                    llm,
                    context,
                    "Comme les horaires ne vous conviennent pas, je notifie le centre médical de votre demande.",
                )
                self.phone_caller.has_note = True
                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return

        if self.phone_caller.intent == Intents.MODIFIER:

            await modify_appointment(
                self.appointment_form,
                self._patient_data,
                self.phone_caller,
                self.doctolib_client,
            )

            self.phone_caller.successful_call = True
            await llm.push_frame(self.sounds["ding2.wav"])
            await self._say_and_wait(
                llm,
                context,
                "Votre rendez-vous est modifié.",
            )
            instruction_msg = (
                "Pour ce rendez-vous, il vous faudra "
                + self.appointment_form.instructions
                if self.appointment_form.instructions
                else ""
            )

            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

    """
    Handle New Appointment
    """

    async def handle_motive(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        motive_arg = args["motive"]

        if utils.remove_accents(motive_arg.lower()) == "manometrie":

            prompt, tools = self.prompts.ask_precise_mano()
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        motive = await self.doctolib_client.get_visit_motive_by_name_or_id(
            motive_arg,
        )

        if not motive:
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                "Désolé, je n'ai pas compris votre raison. Pouvez-vous répéter?",
            )
            return

        motive_name = (
            f"une {motive.type}" if motive.type != MOTIVE.EXAMEN.value else "un examen"
        )

        if motive.type == MOTIVE.ANESTHESIE.value:
            motive_name = "une consultation d'anesthésie"
        elif motive.type == MOTIVE.CONSULTATION.value:
            motive_name = "une consultation"
        elif motive.type == MOTIVE.EXAMEN.value:
            motive_name = "un examen"
        elif motive.type == MOTIVE.MANOMETRIE.value:
            motive_name = "une " + motive.visit_motive_name

        if motive.visit_motive_id == TEST_DEPISTAGE_ADECA_ID:
            prompt, tools = self.prompts.ask_test_adeca_depistage()
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        await self._say_and_wait(
            llm,
            context,
            f"Parfait, vous souhaitez {motive_name}.",
        )

        doctors = await self.doctolib_client.get_doctors()

        self.appointment_form.type = motive.type
        self.appointment_form.visit_motive_name = motive.visit_motive_name
        self.appointment_form.visit_motive_id = motive.visit_motive_id
        self.appointment_form.is_open_motive = motive.open

        if not self.appointment_form.is_open_motive:
            self.phone_caller.has_note = True
            prompt, tools = self.prompts.ask_doctor_name(
                doctors,
                return_node=CaedaTools.handle_motive,
            )
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        elif self.appointment_form.type == MOTIVE.MANOMETRIE.value:
            if "ano-rectale" in self.appointment_form.visit_motive_name.lower():
                await self.handle_mano_ph(
                    "handle_mano_ph",
                    None,
                    {"confirm": False},
                    llm,
                    context,
                    result_callback,
                )
                return

            prompt, tools = self.prompts.ask_mano_ph(
                self.appointment_form.visit_motive_name
            )
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return
        elif (
            self.appointment_form.type == MOTIVE.CONSULTATION.value
            or self.appointment_form.type == MOTIVE.EXAMEN.value
        ):

            prompt, tools = self.prompts.ask_doctor_name(
                doctors,
                return_node=CaedaTools.handle_motive,
            )
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        elif self.appointment_form.type == MOTIVE.ANESTHESIE.value:
            exam_appointment = next(
                (
                    appointment
                    for appointment in self._patient_data.appointments
                    if appointment.type == MOTIVE.EXAMEN.value
                ),
                None,
            )
            if not exam_appointment:
                await self._say_and_wait(
                    llm,
                    context,
                    "Je suis désolé, je ne trouve pas de rendez-vous d'examen. Je notifie le centre médical de votre demande.",
                )
                self.phone_caller.has_note = True
                await self.end_call(
                    "end_call", None, args, llm, context, result_callback
                )
                return

            self.examen_date_for_anhestesia = exam_appointment.start_date
            await self._say_and_wait(
                llm,
                context,
                f"J'ai trouvé votre rendez-vous d'examen du {utils.appointments.format_appointment_to_datetime_with_day_in_french(exam_appointment).split('(')[0]} afin de pouvoir prendre votre consultation d'anesthésie.",
            )
            prompt, tools = self.prompts.ask_take_anticoagulant()
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        else:
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                "Désolé, je n'ai pas compris votre raison. Pouvez-vous répéter?",
            )
            return

    async def handle_doctor_name(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        arg_doctor_name = args["doctor_name"]

        if not arg_doctor_name:
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                "Désolé, je n'ai pas compris votre raison. Pouvez-vous répéter?",
            )
            return

        doctors = await self.doctolib_client.get_doctors()

        if arg_doctor_name == "autre" and self.appointment_form.is_open_motive:
            motive = await self.doctolib_client.get_visit_motive_by_name_or_id(
                str(ALL_DOCTORS_CONSULTATION_MOTIVE_ID),
            )

            self.next_availabilities = (
                await self.doctolib_client.get_next_availabilities(
                    motive, self.company_data.agenda_id
                )
            )

            if not self.next_availabilities:
                await self._say_and_wait(
                    llm,
                    context,
                    "Je suis désolé, il n'y a pas de disponibilité pour le moment.",
                )
                await self.end_call(
                    "end_call",
                    None,
                    args,
                    llm,
                    context,
                    result_callback,
                    return_node=CaedaTools.handle_doctor_name,
                )
                return

            self.appointment_form.type = motive.type
            self.appointment_form.visit_motive_name = motive.visit_motive_name
            self.appointment_form.visit_motive_id = motive.visit_motive_id
            self.appointment_form.is_open_motive = motive.open
            self.appointment_form.instructions = motive.instructions

            if self.appointment_form.visit_motive_id == TEST_DEPISTAGE_ADECA_ID:
                prompt, tools = self.prompts.ask_test_adeca_depistage()
                await self._reply_to_user(result_callback, context, llm, prompt, tools)
                return

            if not self.appointment_form.is_open_motive:
                self.phone_caller.has_note = True
                await self._say_and_wait(
                    llm,
                    context,
                    "Je ne peux pas prendre ce type de rendez-vous. Je notifie le centre médical de votre demande.",
                )
                await self.end_call(
                    "end_call",
                    None,
                    args,
                    llm,
                    context,
                    result_callback,
                    return_node=CaedaTools.handle_doctor_name,
                )

            prompt, tools = self.prompts.suggest_appointment_datetime(
                self.next_availabilities[:2],
                with_doctor=True,
                return_node=CaedaTools.handle_doctor_name,
            )
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        doctor = get_doctor_by_name(arg_doctor_name, doctors)
        if not doctor:
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                "Désolé, je n'ai pas compris le nom du docteur. Pouvez-vous répéter?",
            )
            return

        self.appointment_form.practitioner_id = doctor.practitioner_id
        self.appointment_form.agenda_id = doctor.agenda_id

        if not self.appointment_form.is_open_motive:
            self.phone_caller.has_note = True
            to = f"Docteur {doctor.name}" if doctor else "le centre médical"
            await self._say_and_wait(
                llm,
                context,
                f"Je notifie {to} de votre demande.",
            )
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        new_motive = await self.doctolib_client.get_visit_motive_by_name_or_id(
            f"{self.appointment_form.visit_motive_name} {doctor.name}",
            doctor.name,
        )

        await self._say_and_wait(
            llm,
            context,
            f"Parfait, votre rendez-vous sera avec le docteur {doctor.name.lower()}.",
        )

        if not new_motive:
            await self._say_and_wait(
                llm,
                context,
                "Je notifie le centre médical de votre demande.",
            )
            self.phone_caller.has_note = True
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        self.appointment_form.medecin = doctor.name
        self.appointment_form.agenda_id = doctor.agenda_id
        self.appointment_form.visit_motive_id = new_motive.visit_motive_id
        self.appointment_form.visit_motive_name = new_motive.visit_motive_name
        self.appointment_form.instructions = new_motive.instructions

        self.next_availabilities = await self.doctolib_client.get_next_availabilities(
            new_motive,
            [str(doctor.agenda_id)],
            min_time_difference=3,
            number_of_time_slots=2,
        )

        if not self.next_availabilities:
            await self._say_and_wait(
                llm,
                context,
                "Je suis désolé, il n'y a pas de disponibilité pour le moment.",
            )
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        prompt, tools = self.prompts.suggest_appointment_datetime(
            self.next_availabilities[:2],
        )

        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    async def handle_mano_ph(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        confirm = args["confirm"]
        confirm = confirm == True or "oui" in str(confirm).lower()

        if confirm:
            motive = next(
                (
                    motive
                    for motive in self.company_data.inbound_config_file.visit_motives_categories
                    if motive.visit_motive_id == MANOMETRIE_AND_PH_ID
                ),
                None,
            )

            motive_id = motive.visit_motive_id if motive else None
        else:
            motive_id = self.appointment_form.visit_motive_id

        motive = await self.doctolib_client.get_visit_motive_by_name_or_id(
            str(motive_id)
        )

        logger.info(f"motive: {motive}")
        self.appointment_form.instructions = motive.instructions

        if len(motive.instructions) > 0:
            await self._say_and_wait(
                llm,
                context,
                motive.instructions,
            )

        self.next_availabilities = await self.doctolib_client.get_next_availabilities(
            motive,
            self.company_data.agenda_id,
            min_time_difference=6,
            number_of_time_slots=3,
        )

        unique_dates = set()
        for time_slot in self.next_availabilities:
            unique_dates.add(time_slot.start_date.date())

        for unique_date in unique_dates:
            agenda_day = await self.doctolib_client.get_agenda_day(
                [ANNE_DERLON_ID],
                unique_date,
                [MANOMETRIE_AND_PH_ID, PH_METRIE_ID],
            )
            if len(agenda_day) >= 2:
                self.next_availabilities = list(
                    filter(
                        lambda x: x.start_date.date() != unique_date,
                        self.next_availabilities,
                    )
                )

        if not self.next_availabilities:
            self.phone_caller.has_note = True
            await self._say_and_wait(
                llm,
                context,
                "Je suis désolé, il n'y a pas de disponibilité pour le moment. Je notifie le centre médical de votre demande.",
            )
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        prompt, tools = self.prompts.suggest_appointment_datetime(
            self.next_availabilities[:2]
        )

        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    async def handle_take_anticoagulant(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        confirm = args["confirm"]
        confirm = confirm == True or "oui" in str(confirm).lower()
        contfirm_text = "oui" if confirm else "non"
        self.appointment_form.notes = f"Prise d'anticoagulant: {contfirm_text}"

        min_before_exam = (
            7 if confirm else 2
        )  # 7 if the patient takes anticoagulant, 2 otherwise
        min_date_before_exam = self.examen_date_for_anhestesia - timedelta(
            days=min_before_exam
        )
        current_date = datetime.now()
        max_date_before_exam = self.examen_date_for_anhestesia - timedelta(days=30)
        if current_date.replace(tzinfo=None) > max_date_before_exam.replace(
            tzinfo=None
        ):
            max_date_before_exam = current_date

        if min_date_before_exam.replace(tzinfo=None) < current_date.replace(
            tzinfo=None
        ):
            await self._say_and_wait(
                llm,
                context,
                "Il semble être trop tard pour prendre un rendez-vous pour l'anesthésie. Je notifie le centre médical de votre demande.",
            )
            self.phone_caller.has_note = True
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        self.next_availabilities = []
        for i in range(len(ANESTHESIA_AGENDA_MOTIVE_IDS)):
            motive = await self.doctolib_client.get_visit_motive_by_name_or_id(
                self.appointment_form.visit_motive_name,
                str(ANESTHESIA_AGENDA_MOTIVE_IDS[i][1]),
            )

            next_availabilities = await self.doctolib_client.get_next_availabilities(
                motive,
                [str(ANESTHESIA_AGENDA_MOTIVE_IDS[i][0])],
                min_time_difference=8,
                number_of_time_slots=2,
                from_date=max_date_before_exam,
            )

            if next_availabilities:
                self.next_availabilities.extend(next_availabilities)

        self.next_availabilities = list(
            filter(
                lambda x: max_date_before_exam.replace(tzinfo=None)
                <= x.start_date.replace(tzinfo=None)
                <= min_date_before_exam.replace(tzinfo=None),
                self.next_availabilities,
            )
        )

        if not self.next_availabilities:
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                "Il n'y a pas de disponibilité pour le moment. Je notifie le centre médical de votre demande.",
            )
            self.phone_caller.has_note = True
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        self.next_availabilities = sorted(
            self.next_availabilities, key=lambda x: x.start_date
        )

        prompt, tools = self.prompts.suggest_appointment_datetime(
            self.next_availabilities[:2]
        )

        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    async def handle_test_adeca_depistate(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        confirm = args["confirm"]
        confirm = confirm == True or "oui" in str(confirm).lower()

        self.phone_caller.has_note = True
        await self._say_and_wait(
            llm,
            context,
            "Je notifie le centre médical de votre demande.",
        )
        await self.end_call("end_call", None, args, llm, context, result_callback)
        return

    async def handle_appointment_datetime(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        appointment_datetime = args["appointment_datetime"]

        if appointment_datetime == "autre":
            await self._say_and_wait(
                llm,
                context,
                "Comme les horaires ne vous conviennent pas, je notifie le centre médical de votre demande.",
            )
            self.phone_caller.has_note = True
            await self.end_call("end_call", None, args, llm, context, result_callback)
            return

        if not utils.appointments.is_valid_datetime(
            appointment_datetime, "%Y-%m-%d %H:%M"
        ):
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                "Désolé, je n'ai pas compris votre date et heure. Pouvez-vous répéter?",
            )
            return

        self.appointment_form.start_date = datetime.strptime(
            appointment_datetime, "%Y-%m-%d %H:%M"
        )

        find_appointment = next(
            (
                item
                for item in self.next_availabilities
                if item.start_date.replace(tzinfo=None)
                == self.appointment_form.start_date.replace(tzinfo=None)
            ),
            None,
        )

        if not find_appointment:
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                "Désolé, je n'ai pas compris votre date et heure. Pouvez-vous répéter?",
            )
            return

        self.appointment_form.start_date = find_appointment.start_date
        self.appointment_form.agenda_id = find_appointment.agenda_id
        self.appointment_form.end_date = find_appointment.end_date
        self.appointment_form.medecin = find_appointment.medecin
        if (
            self._patient_data.next_appointment
            and self._patient_data.next_appointment.notes
        ):
            self._patient_data.next_appointment.notes = f"""A modifier son rendez-vous pour le {find_appointment.start_date.strftime("%d/%m/%Y")} car {self._patient_data.next_appointment.notes}"""

        prompt, tools = self.prompts.ask_appointment_confirmation(self.appointment_form)
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    async def handle_how_long_late(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        how_long_late = int(args["minutes"])

        if not isinstance(how_long_late, int):
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                "Désolé, je n'ai pas compris votre réponse. Pouvez-vous répéter?",
            )
            return

        if how_long_late > MAX_RETARD_BEFORE_TASK:
            await self.forward_call(
                "forward_call", None, args, llm, context, result_callback
            )
            return

        self.phone_caller.has_note = True
        await self._say_and_wait(
            llm, context, "Je notifie le centre médical de votre retard"
        )

        await self.end_call("end_call", None, args, llm, context, result_callback)
        return
